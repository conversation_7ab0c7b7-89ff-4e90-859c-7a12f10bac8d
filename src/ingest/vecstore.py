import datetime
import hashlib
import os
import time
import uuid
from typing import List, Dict, Optional
import logging

from dotenv import load_dotenv
from langchain_core.documents import Document
from langchain_pinecone import PineconeVectorStore


from .pinecone_embedding import PineconeEmbedding
from .pinecone_wrapper import PineconeStoreWrapper

load_dotenv()

EMBED_MODEL_NAME = os.environ.get("PINECONE_EMBED_MODEL", "multilingual-e5-large")
# retry logic for Pinecone is must for current plan
PAUSE_SEC = os.environ.get("PINECONE_RETRY_PAUSE_SEC", 10)
MAX_RETRIES = os.environ.get("PINECONE_RETRIES", 5)

logger = logging.getLogger(__name__)

class DocumentVectorStore:
    def __init__(self):
        self.index = os.environ.get("INDEX")
        self.pinecone_wrapper = PineconeStoreWrapper(self.index, max_retries=MAX_RETRIES, sleep_interval=PAUSE_SEC)
        self.pinecone_embeder = PineconeEmbedding(embed_model_name=EMBED_MODEL_NAME, max_retries=MAX_RETRIES, sleep_interval=PAUSE_SEC)

    def upsert(self, doc, chunks):
        docs = self.to_langchain(doc, chunks)
        self.to_vectorstore(docs, doc['community_id'])

    def to_langchain(self, doc, chunks):
        docs = []
        part = 0
        parts = len(chunks)
        utc_now = datetime.datetime.now(datetime.UTC)
        ingest_date = f"{utc_now:%Y-%m-%d %H:%M:%S} +00"
        upload_date = self.format_upload_date(doc['file_upload_date'])
        for text in chunks:
            metadata = {
                "id": f"{doc['id']}#{part}",
                "doc_id": str(doc['id']),
                "source": doc['file_name'],
                "parts": parts,
                "part": part,
                "ingest_date": ingest_date,
                "upload_date": upload_date,
                "text_hash": self.hash_text(text)
            }
            part += 1
            document = Document(page_content=text, metadata=metadata)
            docs.append(document)
        return docs

    def hash_text(self, text):
        return hashlib.sha256(text.encode("utf-8")).hexdigest()

    def format_upload_date(self, update):
        if isinstance(update, datetime.datetime):
            # Convert to UTC if it's a timezone-aware datetime
            upload_date_utc = update.astimezone(datetime.timezone.utc)
            return f"{upload_date_utc:%Y-%m-%d %H:%M:%S} +00"
        else:
            utc_now = datetime.datetime.now(datetime.UTC)
            return f"{utc_now:%Y-%m-%d %H:%M:%S} +00"

    def to_vectorstore(self, docs: List[Document], namespace: str) -> None:
        vecstore = PineconeVectorStore(index_name=self.index, embedding=self.pinecone_embeder, namespace=str(namespace))

        def get_batches(lst, batch_size):
            for i in range(0, len(lst), batch_size):
                yield lst[i:i + batch_size]

        for batch in get_batches(docs, 50):
            retries = 0
            while retries < MAX_RETRIES:
                try:
                    ids = [doc.metadata['id'] for doc in batch]
                    vecstore.add_documents(batch, ids=ids)
                    break
                except Exception as e:
                    retries += 1
                    if retries < MAX_RETRIES:
                        logger.error(f"Exception ingesting batch: {e}. Retrying in {PAUSE_SEC} seconds...")
                        time.sleep(PAUSE_SEC)
                    else:
                        logger.error(f"Failed after {retries} attempts: {e}")
                        raise

    def remove_documents(self, id: uuid.UUID, community_id):
        doc_id = f"{id}#0"
        doc = self._find_doc(doc_id, community_id)
        if doc:
            parts = int(doc['metadata']['parts'])
            ids = [f"{id}#{i}" for i in range(parts)]
            batch_size = 999  # Pinecone supports up to 1000, use 999 to be safe
            for i in range(0, len(ids), batch_size):
                batch_ids = ids[i:i + batch_size]
                self.pinecone_wrapper.delete(ids=batch_ids, namespace=str(community_id))
            logger.info(f"Removed doc {id} from Pinecone.")
            return True
        return False

    def _find_doc(self, doc_id: str, community_id) -> Optional[Dict]:
        result = self.pinecone_wrapper.fetch([doc_id], namespace=str(community_id))
        vectors = getattr(result, 'vectors', {})
        if not vectors:
            return None
        return vectors.get(doc_id)


