import time
import os
from typing import List, Callable
from langchain_core.embeddings import Embeddings
from pinecone import Pinecone
import logging

logger = logging.getLogger(__name__)

EMBED_MODEL_NAME = os.environ.get("PINECONE_EMBED_MODEL", "multilingual-e5-large")

class PineconeEmbedding(Embeddings):

    def __init__(self, embed_model_name: str = EMBED_MODEL_NAME, max_retries: int = 5, sleep_interval: int = 10):
        self.pc = Pinecone()
        self.embed_model_name = embed_model_name
        self.max_retries = max_retries
        self.sleep_interval = sleep_interval

    def _retry(self, func: Callable, message_prefix: str):
        for attempt in range(self.max_retries):
            try:
                return func()
            except Exception as exc:
                if attempt < self.max_retries - 1:
                    logger.error(f"{message_prefix} attempt {attempt + 1} failed: {exc}. Retrying in {self.sleep_interval} seconds...")
                    time.sleep(self.sleep_interval)
                else:
                    logger.error(f"ERROR Pinecone embed retry failed {message_prefix} {exc}")
                    raise

    def embed_documents(self, texts: List[str]) -> List[List[float]]:
        return self._retry(
            lambda: [v['values'] for v in self.pc.inference.embed(
                self.embed_model_name,
                inputs=texts,
                parameters={"input_type": "passage"}
            )],
            "Embed documents"
        )

    def embed_query(self, text: str) -> List[float]:
        return self._retry(
            lambda: self.pc.inference.embed(
                self.embed_model_name,
                inputs=[text],
                parameters={"input_type": "query"}
            )[0]['values'],
            "Embed query"
        )
