import os
import zipfile
import tempfile
from concurrent.futures import ThreadPoolExecutor
from fastapi import APIRouter, UploadFile, File, HTTPException, Query, Response
from fastapi.responses import RedirectResponse
import db
from uuid import UUID
from .process import DocumentProcessor, DocumentStatus
from typing import List, Dict, Any, Optional
from .s3 import S3Helper
import logging

logger = logging.getLogger(__name__)

BASE_URL = "/api/km/ingest"
router = APIRouter(prefix=BASE_URL)

ALLOWED_EXTENSIONS = {".txt", ".html", ".htm", ".md", ".docx", ".pdf", ".pptx", ".xlsx"}

# Thread pool for blocking operations
executor = ThreadPoolExecutor(max_workers=int(os.getenv('MAX_WORKERS', '2')))


def allowed_file(filename: str) -> bool:
    return any(filename.endswith(ext) for ext in ALLOWED_EXTENSIONS)


def schedule_document_processing(document_id: UUID, file_content: bytes, filename: str) -> None:
    """Schedule document upload and processing in background thread"""
    def process_document_task():
        processor = DocumentProcessor()
        processor.upload_and_process_document(document_id, file_content, filename)
    
    # Submit the task to the thread pool
    executor.submit(process_document_task)


def handle_zip_file(file: UploadFile, community_id: UUID, user_id: UUID) -> List[UUID]:
    docs = []
    with tempfile.TemporaryDirectory() as temp_dir:
        zip_path = os.path.join(temp_dir, file.filename)
        content = file.file.read()
        with open(zip_path, 'wb') as zip_file:
            zip_file.write(content)
        with zipfile.ZipFile(zip_path, 'r') as zip_ref:
            zip_ref.extractall(temp_dir)
        processor = DocumentProcessor()
        for root, _, files in os.walk(temp_dir):
            for extracted_file in files:
                extracted_file_path = os.path.join(root, extracted_file)
                if os.path.isfile(extracted_file_path) and allowed_file(extracted_file):
                    with open(extracted_file_path, 'rb') as f:
                        file_content = f.read()
                    # Create document record immediately with UPLOADING status
                    document_id = processor.create_document_record(extracted_file, community_id, user_id)
                    docs.append(document_id)
                    # Handle upload and processing in background
                    schedule_document_processing(document_id, file_content, extracted_file)
    return docs


@router.post("/document", response_model=List[UUID])
def upload_document(
        community_id: UUID,
        user_id: UUID,
        file: UploadFile = File(...)
):
    processor = DocumentProcessor()
    try:
        MAX_SIZE = 104_857_600  # 100MB in bytes
        content_length = file.size
        if content_length and content_length > MAX_SIZE:
            raise HTTPException(status_code=413, detail="File too large. Maximum size allowed is 100MB.")
        if file.filename.endswith('.zip'):
            return handle_zip_file(file, community_id, user_id)
        else:
            if not allowed_file(file.filename):
                raise HTTPException(status_code=400,
                                    detail=f"File type not allowed. Allowed types: {', '.join(ALLOWED_EXTENSIONS)}")
            file_content = file.file.read()
            # Create document record immediately with UPLOADING status
            document_id = processor.create_document_record(file.filename, community_id, user_id)
            # Handle upload and processing in background
            schedule_document_processing(document_id, file_content, file.filename)
            return [document_id]
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"An error occurred: {str(e)}")


@router.get("/documents/{community_id}", response_model=List[dict])
def get_documents_by_community_id(community_id: UUID, include_removed: bool = False):
    return db.getDocumentIngestByCommunityId(community_id, include_removed=include_removed)


@router.get("/documents", response_model=List[dict])
def search_documents(
    response: Response,
    communityId: List[UUID] = Query(..., min_length=1, description="One or more community IDs (required)"),
    status: Optional[List[str]] = Query(None),
    search: Optional[str] = Query(None),
    _offset: int = Query(0, ge=0),
    _limit: int = Query(20, ge=1, le=1000),
    _sort: str = Query("file_upload_date", regex="^(file_name|file_upload_date|updated|status)$"),
    _order: str = Query("DESC", regex="^(ASC|DESC)$")
):
    total_count = db.countDocuments(
        community_ids=communityId,
        statuses=status,
        search=search
    )
    response.headers["x-total-count"] = str(total_count)
    if total_count == 0:
        return []
    return db.searchDocuments(
        community_ids=communityId,
        statuses=status,
        search=search,
        offset=_offset,
        limit=_limit,
        sort_field=_sort,
        sort_order=_order
    )


@router.get("/document/{document_id}", response_model=Dict[str, Any])
def get_document_id(document_id: UUID):
    doc = db.getDocumentIngestById(document_id)
    if not doc:
        raise HTTPException(status_code=404, detail="Document not found")
    return doc

@router.delete("/document/{id}")
def delete_document_by_id(id: UUID):
    processor = DocumentProcessor()
    processor.remove_document(id)
    return {"detail": "Document deleted successfully"}


@router.get("/file/{document_id}")
def get_file_link(document_id: UUID):
    doc = db.getDocumentIngestById(document_id)
    if not doc:
        raise HTTPException(status_code=404, detail="Document not found")
    file_location = doc['file_location']
    if not file_location:
        raise HTTPException(status_code=400, detail="File is still uploading, please wait")
    s3_helper = S3Helper()
    file_link = s3_helper.file_link(file_location, expiration=3600)
    if not file_link:
        raise HTTPException(status_code=500, detail="Could not generate file link")
    return RedirectResponse(url=file_link)


def process_pending_documents():
    """Process documents that were uploaded but not ingested, including OCR documents"""
    processor = DocumentProcessor()
    
    # Get all documents with UPLOADED status
    uploaded_docs = db.getDocumentIngestByStatus(DocumentStatus.UPLOADED.value)
    logger.info(f"process_pending_documents, uploaded_docs: {len(uploaded_docs)}")
    
    # Get documents stuck in UPLOADING status (likely due to server restart)
    uploading_docs = db.getDocumentIngestByStatus(DocumentStatus.UPLOADING.value)
    logger.info(f"process_pending_documents, uploading_docs (stuck): {len(uploading_docs)}")
    
    # Get documents in OCR status (OCR was in progress during restart)
    ocr_docs = db.getDocumentIngestByStatus(DocumentStatus.OCR.value)
    logger.info(f"process_pending_documents, ocr_docs: {len(ocr_docs)}")
    
    # Process regular uploaded documents
    for doc in uploaded_docs:
        logger.info(f"Processing uploaded document during startup, id: {doc['id']}")
        try:
            processor.process_document(doc['id'])
        except Exception as e:
            logger.error(f"Error processing uploaded document {doc['id']}: {str(e)}")

    # Mark stuck uploading documents as error
    for doc in uploading_docs:
        try:
            db.updateDocumentIngestStatus(doc['id'], DocumentStatus.ERROR.value, 
                                        "Upload was interrupted during server restart")
            logger.info(f"Marked document {doc['id']} as ERROR due to interrupted upload")
        except Exception as e:
            logger.error(f"Error updating stuck document {doc['id']}: {str(e)}")

    # Continue OCR processing for documents that were in OCR status
    for doc in ocr_docs:
        logger.info(f"Continuing OCR processing during startup, id: {doc['id']}")
        try:
            processor.continue_ocr_processing(doc['id'])
        except Exception as e:
            logger.error(f"Error continuing OCR for document {doc['id']}: {str(e)}")


def startup_event():
    """Startup event handler to process pending documents"""
    logger.info("startup_event")
    import threading
    threading.Thread(target=process_pending_documents, daemon=True).start()
    
    # Also start a background task to periodically check OCR documents
    threading.Thread(target=ocr_background_monitor, daemon=True).start()


def ocr_background_monitor():
    """Background task to monitor and continue OCR processing"""
    import time
    
    while True:
        try:
            time.sleep(30)  # Check every 30 seconds
            processor = DocumentProcessor()
            ocr_docs = db.getDocumentIngestByStatus(DocumentStatus.OCR.value)
            
            for doc in ocr_docs:
                try:
                    processor.continue_ocr_processing(doc['id'])
                except Exception as e:
                    logger.error(f"Background OCR monitor error for document {doc['id']}: {e}")
                    
        except Exception as e:
            logger.error(f"OCR background monitor error: {e}")
            time.sleep(60)  # Wait longer on error


router.add_event_handler("startup", startup_event)
