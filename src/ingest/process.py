import uuid
from enum import Enum
import re
import unicodedata
import db
from .chunker import Document<PERSON>hunker
from .parse import DocumentParser, OCRInProgressException
from .s3 import S3Helper
from .vecstore import DocumentVectorStore
import logging

logger = logging.getLogger(__name__)

class DocumentStatus(Enum):
    ERROR = 'ERROR'
    UPLOADING = 'UPLOADING'
    UPLOADED = 'UPLOADED'
    OCR = 'OCR'
    INGESTED = 'INGESTED'
    REMOVED = 'REMOVED'


class DocumentProcessor:
    def __init__(self):
        self.s3_helper = S3Helper()
        self.parser = DocumentParser()
        self.chunker = DocumentChunker()
        self.vecstore = DocumentVectorStore()

    def create_document_record(self, file_name: str, community_id: uuid.UUID,
                              user_id: uuid.UUID) -> uuid.UUID:
        """Create document record with UPLOADING status immediately"""
        try:
            file_name = to_ascii(file_name)
            id = uuid.uuid4()
            # Create record with UPLOADING status and NULL location (will be updated after upload)
            db.insertDocumentIngest(id, community_id, DocumentStatus.UPLOADING.value, None, file_name, None, None, user_id)
            return id
        except Exception as e:
            logger.error(f"create document record error, file_name: {file_name}, community_id: {community_id}, user_id: {user_id} {e}")
            raise

    def upload_and_process_document(self, document_id: uuid.UUID, file_content: bytes, file_name: str):
        """Upload file to S3 and then process document - runs in background"""
        try:
            # First upload to S3
            location = self.s3_helper.file_upload(file_name, file_content, document_id)
            
            # Update document record with file location and UPLOADED status
            db.updateDocumentIngestLocationAndStatus(document_id, location, DocumentStatus.UPLOADED.value)
            logger.info(f"File uploaded to S3, document_id: {document_id}, location: {location}")
            
            # Then process the document
            self.process_document(document_id, file_content)
            
        except Exception as e:
            logger.error(f"upload_and_process_document error, document_id: {document_id}, file_name: {file_name} {e}")
            db.updateDocumentIngestStatus(document_id, DocumentStatus.ERROR.value, str(e))
            raise

    def upload_file(self, file_content: bytes, file_name: str, community_id: uuid.UUID,
                    user_id: uuid.UUID) -> uuid.UUID | None:
        """Legacy method - kept for backward compatibility"""
        try:
            file_name = to_ascii(file_name)
            id = uuid.uuid4()
            location = self.s3_helper.file_upload(file_name, file_content, id)
            db.insertDocumentIngest(id, community_id, DocumentStatus.UPLOADED.value, None, file_name, location, None, user_id)
            return id
        except Exception as e:
            logger.error(f"file upload error, file_name: {file_name}, community_id: {community_id}, user_id: {user_id} {e}")
            raise

    def process_document(self, id: uuid.UUID, file_content: bytes = None):
        doc = db.getDocumentIngestById(id)
        if doc:
            try:
                logger.info(f"start process document, id: {id}, doc: {doc}")
                if not file_content:
                    file_content = self.s3_helper.file_download(doc['file_location'])

                try:
                    text = self.parser.extract_markdown(file_content, doc['file_name'], id)
                    chunks = self.chunker.chunk(text)
                    self.vecstore.upsert(doc, chunks)
                    db.updateDocumentIngestStatus(id, DocumentStatus.INGESTED.value)
                    logger.info(f"done process document, id: {id}, doc: {doc}")
                except OCRInProgressException as e:
                    # OCR was started but not completed, document is in OCR status
                    logger.info(f"Document {id} is now in OCR processing: {e}")
                    # Don't mark as error, let background process handle it

            except Exception as e:
                logger.error(f"failed process document, id: {id}, doc: {doc}")
                db.updateDocumentIngestStatus(id, DocumentStatus.ERROR.value, str(e))
                raise
        else:
            logger.error(f"not found process document, id: {id}, doc: {doc}")
            raise Exception(f"document not found in db, id: {id}")

    def remove_document(self, id: uuid.UUID, full=False):
        doc = db.getDocumentIngestById(id)
        if doc:
            # Remove original file
            self.s3_helper.file_remove(doc['file_location'])
            
            # Remove OCR result file if it exists
            try:
                ocr_location = f"{id}/ocr.txt"
                self.s3_helper.file_remove(ocr_location)
                logger.info(f"Removed OCR result file for document {id}")
            except Exception as e:
                logger.debug(f"No OCR result file to remove for document {id}: {e}")
            
            self.vecstore.remove_documents(id, doc['community_id'])
            db.updateDocumentIngestStatus(id, DocumentStatus.REMOVED.value)
            if full:
                db.deleteDocumentIngest(id)

    def _complete_ocr_ingestion(self, doc_id: uuid.UUID, ocr_location: str, doc: dict) -> bool:
        """
        Complete document ingestion using OCR result file.
        Returns True if successful, False otherwise.
        """
        try:
            logger.info(f"Processing OCR result file at {ocr_location} for document {doc_id}")
            
            ocr_content = self.s3_helper.file_download(ocr_location)
            text = ocr_content.decode('utf-8')
            
            chunks = self.chunker.chunk(text)
            self.vecstore.upsert(doc, chunks)
            db.updateDocumentIngestStatus(doc_id, DocumentStatus.INGESTED.value)
            
            logger.info(f"OCR document processing completed, id: {doc_id}")
            return True
            
        except Exception as e:
            logger.error(f"Error processing OCR result for document {doc_id}: {e}")
            db.updateDocumentIngestStatus(doc_id, DocumentStatus.ERROR.value, f"Failed to process OCR result: {e}")
            return False

    def continue_ocr_processing(self, doc_id: uuid.UUID) -> bool:
        """
        Continue OCR processing for a document and complete ingestion if OCR is done.
        Returns True if document was fully processed, False if still in progress.
        """
        try:
            doc = db.getDocumentIngestById(doc_id)
            if not doc or doc['status'] != DocumentStatus.OCR.value:
                logger.error(f"Document {doc_id} is not in OCR status")
                return False

            error_message = doc.get('error_message', '')
            
            # Case 1: OCR result file is already available
            if error_message.startswith('OCR_FILE_LOCATION:'):
                ocr_location = error_message.replace('OCR_FILE_LOCATION:', '')
                return self._complete_ocr_ingestion(doc_id, ocr_location, doc)
            
            # Case 2: OCR task is still in progress
            elif error_message.startswith('OCR_TASK_ID:'):
                if self.parser.continue_ocr_process(doc_id):
                    # OCR completed, get updated document with file location
                    updated_doc = db.getDocumentIngestById(doc_id)
                    if not updated_doc:
                        logger.error(f"Could not retrieve updated document record for {doc_id}")
                        return False
                    
                    updated_error_message = updated_doc.get('error_message', '')
                    if updated_error_message.startswith('OCR_FILE_LOCATION:'):
                        ocr_location = updated_error_message.replace('OCR_FILE_LOCATION:', '')
                        return self._complete_ocr_ingestion(doc_id, ocr_location, updated_doc)
                    else:
                        logger.error(f"OCR claimed to be complete but no file location found for document {doc_id}")
                        db.updateDocumentIngestStatus(doc_id, DocumentStatus.ERROR.value, "OCR completed but no result file found")
                        return False
                else:
                    # OCR still in progress
                    logger.info(f"OCR still in progress for document {doc_id}")
                    return False
            
            # Case 3: Invalid state
            else:
                logger.error(f"Invalid error_message format for OCR document {doc_id}: {error_message}")
                db.updateDocumentIngestStatus(doc_id, DocumentStatus.ERROR.value, "Invalid OCR state")
                return False
                
        except Exception as e:
            logger.error(f"Error continuing OCR processing for document {doc_id}: {e}")
            db.updateDocumentIngestStatus(doc_id, DocumentStatus.ERROR.value, str(e))
            return False

def to_ascii(s: str) -> str:
    # Normalize unicode characters to closest ASCII representation
    normalized = unicodedata.normalize('NFKD', s).encode('ascii', 'ignore').decode('ascii')
    # Replace any remaining non-alphanumeric characters with underscores
    ascii_str = re.sub(r'[^a-zA-Z0-9._-]', '_', normalized)
    return ascii_str
