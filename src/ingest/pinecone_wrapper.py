import time
import logging
from typing import List, Dict, Optional, Callable
from pinecone import Pinecone

logger = logging.getLogger(__name__)

class PineconeStoreWrapper:
    def __init__(self, index_name, max_retries: int = 5, sleep_interval: int = 10):
        self.index_name = index_name
        pc = Pinecone()
        self.index = pc.Index(self.index_name)
        self.max_retries = max_retries
        self.sleep_interval = sleep_interval

    def _retry(self, func: Callable, message_prefix: str):
        for attempt in range(self.max_retries):
            try:
                return func()
            except Exception as exc:
                if attempt < self.max_retries - 1:
                    logger.error(f"{message_prefix} attempt {attempt + 1} failed: {exc}. Retrying in {self.sleep_interval} seconds...")
                    time.sleep(self.sleep_interval)
                else:
                    logger.error(f"ERROR Pinecone store retry failed {message_prefix} {exc}")
                    raise

    def fetch(self, ids: List[str], namespace: str) -> Dict:
        return self._retry(
            lambda: self.index.fetch(ids=ids, namespace=namespace),
            "Fetch"
        )

    def delete(self, ids: Optional[List[str]] = None, namespace: Optional[str] = None, filter: Optional[dict] = None):
        return self._retry(
            lambda: self.index.delete(ids=ids, namespace=namespace, filter=filter),
            "Delete"
        )

    def query(self, vector: List[float], top_k: int, includeMetadata: bool, namespace: str) -> Dict:
        return self._retry(
            lambda: self.index.query(vector=vector, top_k=top_k, includeMetadata=includeMetadata, namespace=namespace),
            "Query"
        )

    def describe_index_stats(self) -> Dict:
        return self._retry(
            lambda: self.index.describe_index_stats(),
            "Describe stats"
        )

    def delete_namespace(self, name: str):
        return self._retry(
            lambda: self.index.delete(delete_all=True, namespace=str(name)),
            "Delete namespace "+name
        )
