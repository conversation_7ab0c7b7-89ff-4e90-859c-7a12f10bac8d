import os
import logging

import boto3
from botocore.client import Config

logger = logging.getLogger(__name__)

class S3Helper:
    def __init__(self):
        region = os.environ.get("AWS_REGION", "eu-central-1")
        self.s3 = boto3.client('s3', region_name=region,
                               config=Config(signature_version='s3v4'))
        self.bucket = os.environ.get("AWS_S3_BUCKET")

    def file_upload(self, file_name, file_content, doc_id):
        location = f'{doc_id}/{file_name}'
        self.s3.put_object(
            Bucket=self.bucket,
            Key=location,
            Body=file_content,
            ContentType=_get_content_type(file_name)  # used for download via url from s3, see file_link()
        )
        return location

    def file_download(self, location):
        response = self.s3.get_object(
            Bucket=self.bucket,
            Key=location
        )
        return response['Body'].read()

    def file_link(self, location, expiration=3600):
        try:
            response = self.s3.generate_presigned_url('get_object',
                                                      Params={'Bucket': self.bucket,
                                                              'Key': location},
                                                      ExpiresIn=expiration)

            logger.info(f"file link for location: {location}, expiration: {expiration}, url: {response}")
        except Exception as e:
            logger.error(f"error file link for location {location} {e}")
            return None

        return response

    def file_remove(self, location):
        self.s3.delete_object(
            Bucket=self.bucket,
            Key=location
        )


def _get_content_type(file_name: str) -> str:
    """Get content type based on file extension"""
    ext = os.path.splitext(file_name)[1].lower()
    content_types = {
        '.txt': 'text/plain',
        '.html': 'text/html',
        '.htm': 'text/html',
        '.md': 'text/markdown',
        '.docx': 'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
        '.pdf': 'application/pdf',
        '.pptx': 'application/vnd.openxmlformats-officedocument.presentationml.presentation',
        '.xlsx': 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
    }
    return content_types.get(ext, 'application/octet-stream')
