import os
import uuid
import logging
import zipfile
import html
import re
import time
import xml.etree.ElementTree as ET
import requests
from io import BytesIO

# from docling.document_converter import DocumentConverter
from markitdown import MarkItDown
import pymupdf
import db

logger = logging.getLogger(__name__)

class DocumentParser:
    MAX_CHARACTERS = 1_000_000  # Maximum allowed characters in a document
    MIN_EXTRACTED_CHARACTERS = 100  # Minimum characters needed to avoid OCR fallback
    
    def __init__(self):
        self.temp = os.environ.get("TEMP_DIR", "/temp")
        os.makedirs(self.temp, exist_ok=True)
        
        # ABBYY OCR configuration from environment variables
        self.abbyy_host = os.environ.get("ABBYY_HOST", "http://cloud-eu.ocrsdk.com")
        self.abbyy_app_id = os.environ.get("ABBYY_APP_ID")
        self.abbyy_password = os.environ.get("ABBYY_PASSWORD")

    def _get_docx_character_count(self, file_content: bytes) -> int:
        """
        Extract text length directly from docx file bytes without writing to disk
        Memory efficient implementation using streaming XML tag removal
        """
        try:
            total_length = 0
            
            with zipfile.ZipFile(BytesIO(file_content), 'r') as docx_zip:
                try:
                    with docx_zip.open('word/document.xml') as doc_xml:
                        # Process the XML content in chunks to avoid loading entire document
                        chunk_size = 8192  # 8KB chunks
                        buffer = ""
                        
                        while True:
                            chunk = doc_xml.read(chunk_size)
                            if not chunk:
                                break
                                
                            # Convert bytes to string and add to buffer
                            buffer += chunk.decode('utf-8', errors='ignore')
                            
                            # Process all complete w:t elements in the buffer
                            while '<w:t' in buffer and '</w:t>' in buffer:
                                # Find the start of w:t tag
                                start_idx = buffer.find('<w:t')
                                if start_idx == -1:
                                    break
                                    
                                # Find the end of the opening tag
                                tag_end_idx = buffer.find('>', start_idx)
                                if tag_end_idx == -1:
                                    break
                                    
                                # Find the closing tag
                                close_tag_idx = buffer.find('</w:t>', tag_end_idx)
                                if close_tag_idx == -1:
                                    break
                                    
                                # Extract text content between tags
                                text_content = buffer[tag_end_idx + 1:close_tag_idx]
                                # Handle XML entities (like &amp;, &lt;, etc.)
                                text_content = html.unescape(text_content)
                                total_length += len(text_content)
                                
                                # Remove the processed part from buffer
                                buffer = buffer[close_tag_idx + 6:]  # 6 is length of '</w:t>'
                            
                            # Keep only the last part that might contain incomplete tags
                            if '<w:t' in buffer:
                                last_tag_start = buffer.rfind('<w:t')
                                buffer = buffer[last_tag_start:]
                            else:
                                buffer = ""
                        
                        # Process any remaining complete w:t elements in the final buffer
                        while '<w:t' in buffer and '</w:t>' in buffer:
                            start_idx = buffer.find('<w:t')
                            if start_idx == -1:
                                break
                                
                            tag_end_idx = buffer.find('>', start_idx)
                            if tag_end_idx == -1:
                                break
                                
                            close_tag_idx = buffer.find('</w:t>', tag_end_idx)
                            if close_tag_idx == -1:
                                break
                                
                            text_content = buffer[tag_end_idx + 1:close_tag_idx]
                            text_content = html.unescape(text_content)
                            total_length += len(text_content)
                            
                            buffer = buffer[close_tag_idx + 6:]
                    
                except KeyError:
                    logger.warning("document.xml not found in docx file")
                    return 0
                    
            return total_length
        except Exception as e:
            logger.error(f"Error counting characters in docx file: {e}")
            return 0

    def _get_text_character_count(self, file_content: bytes, file_extension: str) -> int:
        """
        Count characters in text-based files
        For HTML files, strip HTML tags before counting
        """
        try:
            # Decode bytes to string
            text_content = file_content.decode('utf-8', errors='ignore')
            
            # For HTML files, strip HTML tags before counting
            if file_extension.lower() in {'.html', '.htm'}:
                # Remove HTML tags using regex
                text_content = re.sub(r'<[^>]+>', '', text_content)
                # Handle HTML entities
                text_content = html.unescape(text_content)
            
            return len(text_content)
        except Exception as e:
            logger.error(f"Error counting characters in text file: {e}")
            return 0

    def _get_pdf_character_count(self, file_content: bytes) -> int:
        """
        Extract text length directly from PDF file bytes using PyMuPDF (pymupdf)
        Memory efficient implementation processing page by page
        """
        try:
            total_length = 0
            doc = pymupdf.open(stream=file_content, filetype="pdf")
            
            # Process page by page to avoid loading all text at once
            for page_num in range(len(doc)):
                page = doc.load_page(page_num)
                text = page.get_text()
                if text:
                    total_length += len(text)
                    
            doc.close()
            return total_length
        except Exception as e:
            logger.error(f"Error counting characters in PDF file: {e}")
            return 0

    def extract_markdown(self, file_content: bytes, file_name: str, doc_id: uuid.UUID):
        supported_extensions = {".txt", ".html", ".htm", ".md", ".docx", ".pdf", ".pptx", ".xlsx"}
        _, ext = os.path.splitext(file_name)
        ext = ext.lower()

        if ext not in supported_extensions:
            logger.error(f"Error: Unsupported file type: {ext}")
            raise ValueError(f"Unsupported file type: {ext}")

        # Check character count for text-based files before parsing
        if ext in {".txt", ".html", ".htm", ".md"}:
            char_count = self._get_text_character_count(file_content, ext)
            if char_count > self.MAX_CHARACTERS:
                logger.error(f"Text file {file_name} has {char_count:,} characters, exceeding limit of {self.MAX_CHARACTERS:,}")
                raise ValueError(f"Document too large: {char_count:,} characters (limit: {self.MAX_CHARACTERS:,})")
            
            # For simple text files, return decoded content directly
            if ext in {".txt", ".md"}:
                return file_content.decode("utf8")

        # Check character count for DOCX files before parsing
        if ext == ".docx":
            char_count = self._get_docx_character_count(file_content)
            if char_count > self.MAX_CHARACTERS:
                logger.error(f"DOCX file {file_name} has {char_count:,} characters, exceeding limit of {self.MAX_CHARACTERS:,}")
                raise ValueError(f"Document too large: {char_count:,} characters (limit: {self.MAX_CHARACTERS:,})")

        # Check character count for PDF files before parsing
        if ext == ".pdf":
            char_count = self._get_pdf_character_count(file_content)
            if char_count > self.MAX_CHARACTERS:
                logger.error(f"PDF file {file_name} has {char_count:,} characters, exceeding limit of {self.MAX_CHARACTERS:,}")
                raise ValueError(f"Document too large: {char_count:,} characters (limit: {self.MAX_CHARACTERS:,})")

        unique_file_path = os.path.join(self.temp, f"{doc_id}_{file_name}")
        with open(unique_file_path, "wb") as f:
            f.write(file_content)

        try:
            result_text = self._try_markitdown_parser(unique_file_path)
            # Check if we extracted enough meaningful text (at least 100 characters)
            if not (result_text and len(result_text.strip()) >= self.MIN_EXTRACTED_CHARACTERS):
                logger.info(f"MarkItDown extracted insufficient text ({len(result_text.strip()) if result_text else 0} chars), trying ABBYY OCR for {file_name}")
                result_text = self.start_ocr_process(file_content, file_name, doc_id)
            return result_text
        except Exception as e:
            logger.error(f"Failed to extract markdown {file_name} {doc_id} {e}")
            raise e
        finally:
            if os.path.exists(unique_file_path):
                os.remove(unique_file_path)

    def _try_markitdown_parser(self, file_path: str) -> str:
        try:
            return MarkItDown().convert(file_path).text_content
        except Exception:
            logger.error(f"Failed to parse with markitdown: {file_path}")
            return ""

    def start_ocr_process(self, file_content: bytes, file_name: str, doc_id: uuid.UUID) -> str:
        """
        Start OCR process and update document status to OCR.
        Always raises OCRInProgressException - processing will be handled by the OCR monitor.
        """
        from .process import DocumentStatus
        
        if not self.abbyy_app_id or not self.abbyy_password:
            logger.error("ABBYY OCR credentials not configured. Set ABBYY_APP_ID and ABBYY_PASSWORD environment variables.")
            raise ValueError("ABBYY OCR credentials not configured")

        try:
            session = requests.Session()
            
            # Submit file for OCR processing to DOCX format
            logger.info(f"Submitting {file_name} to ABBYY OCR service")
            resp = session.post(
                f"{self.abbyy_host}/processImage",
                params={"exportFormat": "docx"},
                data=file_content,
                auth=(self.abbyy_app_id, self.abbyy_password),
                timeout=60
            )
            resp.raise_for_status()
            
            task = ET.fromstring(resp.text).find("task")
            if task is None:
                raise RuntimeError("No task element found in ABBYY response")
            
            task_id = task.get("id")
            if not task_id:
                raise RuntimeError("No task ID returned from ABBYY")

            # Update document status to OCR and store task ID
            db.updateDocumentIngestStatus(doc_id, DocumentStatus.OCR.value, f"OCR_TASK_ID:{task_id}")

            # OCR submitted successfully, will be handled by background monitor
            logger.info(f"OCR task {task_id} submitted for {file_name}, document {doc_id} marked for background processing")
            raise OCRInProgressException(f"OCR processing started for {file_name}")

        except OCRInProgressException:
            raise  # Re-raise OCR in progress exception
        except Exception as e:
            logger.error(f"ABBYY OCR processing failed for {file_name}: {e}")
            raise e

    def continue_ocr_process(self, doc_id: uuid.UUID) -> bool:
        """
        Continue OCR process for a document that was interrupted by service restart.
        Returns True if processing completed successfully, False otherwise.
        """
        from .process import DocumentStatus
        
        doc = db.getDocumentIngestById(doc_id)
        if not doc or doc['status'] != DocumentStatus.OCR.value:
            logger.error(f"Document {doc_id} is not in OCR status")
            return False

        # Extract task ID from error_message field
        error_message = doc.get('error_message', '')
        if not error_message or not error_message.startswith('OCR_TASK_ID:'):
            logger.error(f"No OCR task ID found for document {doc_id}")
            db.updateDocumentIngestStatus(doc_id, DocumentStatus.ERROR.value, "OCR task ID not found after restart")
            return False

        task_id = error_message.replace('OCR_TASK_ID:', '')
        
        try:
            session = requests.Session()
            
            # Check task status
            resp = session.get(
                f"{self.abbyy_host}/getTaskStatus",
                params={"taskId": task_id},
                auth=(self.abbyy_app_id, self.abbyy_password),
                timeout=30
            )
            resp.raise_for_status()
            
            task = ET.fromstring(resp.text).find("task")
            if task is None:
                raise RuntimeError("No task element found in status response")
            
            status = task.get("status")
            logger.info(f"Resuming OCR for document {doc_id}, status: {status}")
            
            if status == "Completed":
                # Process the result (includes cleanup)
                result_text = self._process_ocr_result(session, task, doc['file_name'], doc_id)
                if result_text:
                    return True
            elif status == "Failed":
                # Clean up failed task
                error_msg = task.get("error", "Unknown OCR error")
                try:
                    cleanup_resp = session.get(
                        f"{self.abbyy_host}/deleteTask", 
                        params={"taskId": task_id}, 
                        auth=(self.abbyy_app_id, self.abbyy_password),
                        timeout=30
                    )
                    cleanup_resp.raise_for_status()
                    logger.info(f"Successfully cleaned up failed ABBYY task {task_id} for document {doc_id}")
                except Exception as cleanup_error:
                    logger.warning(f"Failed to cleanup failed ABBYY task {task_id} for document {doc_id}: {cleanup_error}")
                
                db.updateDocumentIngestStatus(doc_id, DocumentStatus.ERROR.value, f"OCR failed: {error_msg}")
                logger.error(f"OCR failed for document {doc_id}: {error_msg}")
            else:
                # Still processing, continue waiting - do not cleanup
                logger.info(f"OCR still in progress for document {doc_id}")
                return False

        except Exception as e:
            logger.error(f"Error continuing OCR for document {doc_id}: {e}")
            db.updateDocumentIngestStatus(doc_id, DocumentStatus.ERROR.value, f"OCR continuation failed: {e}")
            
        return False

    def _process_ocr_result(self, session: requests.Session, task, file_name: str, doc_id: uuid.UUID) -> str:
        """
        Process completed OCR result and return extracted text.
        """
        from .process import DocumentStatus
        from .s3 import S3Helper
        
        task_id = task.get("id")
        cleanup_needed = True
        s3_helper = S3Helper()
        
        try:
            # Download the DOCX result
            result_url = task.get("resultUrl")
            if not result_url:
                raise RuntimeError("No result URL returned from ABBYY")
            
            logger.info(f"Downloading OCR result for {file_name}")
            docx_resp = session.get(result_url, timeout=120)
            docx_resp.raise_for_status()
            docx_content = docx_resp.content

            # Process the OCR'd DOCX with MarkItDown
            ocr_docx_path = os.path.join(self.temp, f"ocr_{doc_id}_{file_name}.docx")
            try:
                with open(ocr_docx_path, "wb") as f:
                    f.write(docx_content)
                
                logger.info(f"Processing OCR result with MarkItDown for {file_name}")
                result_text = MarkItDown().convert(ocr_docx_path).text_content
                
                if result_text and len(result_text.strip()) >= self.MIN_EXTRACTED_CHARACTERS:
                    # Save OCR result to S3 using existing file_upload function
                    ocr_location = s3_helper.file_upload("ocr.txt", result_text.encode('utf-8'), doc_id)
                    logger.info(f"ABBYY OCR successfully extracted {len(result_text.strip())} characters for {file_name} and saved to S3 at {ocr_location}")
                    
                    # Update status to OCR with file location instead of task ID
                    db.updateDocumentIngestStatus(doc_id, DocumentStatus.OCR.value, f"OCR_FILE_LOCATION:{ocr_location}")
                    return result_text
                else:
                    logger.warning(f"ABBYY OCR still produced insufficient text for {file_name}")
                    db.updateDocumentIngestStatus(doc_id, DocumentStatus.ERROR.value, f"OCR produced insufficient text for {file_name}")
                    return result_text or ""
                    
            finally:
                if os.path.exists(ocr_docx_path):
                    os.remove(ocr_docx_path)

        except Exception as e:
            logger.error(f"Error processing OCR result for {file_name}: {e}")
            db.updateDocumentIngestStatus(doc_id, DocumentStatus.ERROR.value, f"OCR result processing failed: {e}")
            return ""
        finally:
            # Always cleanup ABBYY task resources
            if cleanup_needed and task_id:
                try:
                    cleanup_resp = session.get(
                        f"{self.abbyy_host}/deleteTask", 
                        params={"taskId": task_id}, 
                        auth=(self.abbyy_app_id, self.abbyy_password),
                        timeout=30
                    )
                    cleanup_resp.raise_for_status()
                    logger.info(f"Successfully cleaned up ABBYY task {task_id} for {file_name}")
                    cleanup_needed = False
                except Exception as cleanup_error:
                    logger.warning(f"Failed to cleanup ABBYY task {task_id} for {file_name}: {cleanup_error}")


class OCRInProgressException(Exception):
    """Exception raised when OCR processing is started but not yet complete"""
    pass



