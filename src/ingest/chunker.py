from langchain_text_splitters import MarkdownTextSplitter
import logging

logger = logging.getLogger(__name__)


class DocumentChunker:
    """
    A class to chunk Markdown documents based on Markdown syntax
    (headers, lists, code blocks, etc.) and a maximum chunk length.

    Uses Langchain's MarkdownTextSplitter for effective Markdown parsing.
    """

    def __init__(self, chunk_size: int = 1500, chunk_overlap: int = 0):
        """
        Initializes the DocumentChunker.

        Args:
            chunk_size (int): The target maximum size of each chunk (in characters).
                              Defaults to 1500.
            chunk_overlap (int): The number of characters to overlap between consecutive chunks.
                                 Helps maintain context.
        """
        if chunk_overlap >= chunk_size:
            raise ValueError("chunk_overlap must be smaller than chunk_size")

        self.chunk_size = chunk_size
        self.chunk_overlap = chunk_overlap

        # Use MarkdownTextSplitter for intelligent splitting based on Markdown structure.
        # It already includes separators for headers, lists, code blocks, paragraphs etc.
        # It inherits from RecursiveCharacterTextSplitter.
        self.text_splitter = MarkdownTextSplitter(
            chunk_size=self.chunk_size,
            chunk_overlap=self.chunk_overlap,
            length_function=len,
            # is_separator_regex=False # Default for MarkdownTextSplitter
        )

    def chunk(self, text: str) -> list[str]:
        """
        Splits the Markdown text into chunks.

        Args:
            text (str): The Markdown text to be chunked.

        Returns:
            list[str]: A list of text chunks. Returns an empty list if the
                       input text is empty or contains only whitespace.
        """
        if not text or text.strip() == "":
            logger.info("Input text is empty or whitespace only, returning empty list.")
            return []

        chunks = self.text_splitter.split_text(text)

        # Optional: Filter out any potential empty strings, although the splitter
        # should generally handle this.
        processed_chunks = [chunk for chunk in chunks if chunk.strip()]

        return processed_chunks


# --- Example Usage ---
if __name__ == "__main__":
    markdown_content = """
# Document Title

This is the introduction. It explains the purpose of the document.
It might have multiple sentences.

## Section 1: Concepts

This section introduces key concepts.

*   Concept A: Description A.
*   Concept B: Description B.

More text explaining the concepts. Lorem ipsum dolor sit amet, consectetur adipiscing elit.

### Subsection 1.1

Details about Concept A. Sed do eiusmod tempor incididunt ut labore et dolore magna aliqua.

**Important Note:** Remember to check the prerequisites. Ut enim ad minim veniam, quis nostrud exercitation ullamco laboris nisi ut aliquip ex ea commodo consequat.

```python
# Example Python code
def hello(name):
    logger.info(f"Hello, {name}!")

hello("World")

"""
    chunker = DocumentChunker(chunk_size=400, chunk_overlap=0)

    # Chunk the document
    chunks = chunker.chunk(markdown_content)

    # Print the chunks

    for i, chunk_text in enumerate(chunks):
        logger.info(f"--- Chunk {i + 1} (Length: {len(chunk_text)}) ---")
        logger.info(chunk_text)
        logger.info("-" * (len(f"--- Chunk {i + 1} (Length: {len(chunk_text)}) ---")))  # Separator