MAIN = "main"
GEN = "gen"
prompts = {"auto": {}, "en": {}, "pl": {}, "da": {}}
tax_qa_prompt= {"en": {}}

prompts["auto"][MAIN] = """Answer the question based only on the following context:
{context}

Answer the question in the language of the question. Question: {question}
"""


prompts["en"][MAIN] = """Answer the question based only on the following context:
{context}

Answer the question in English language. Question: {question}
"""


prompts["pl"][MAIN] = """Odpowiedz na pytanie bazując tylko na fragmencie tekstu poniżej:
{context}

Odpowiedz w języku polskim na pytanie: {question}
"""


prompts["da"][MAIN] = """Besvar kun spørgsmålet ud fra følgende sammenhæng:
{context}

Be<PERSON>var spørgsmålet på dansk. Spørgsmål: {question}

Svar:
"""


prompts["auto"][GEN] = """Given a chat history and the latest user question \
which might reference context in the chat history, formulate a standalone question \
which can be understood without the chat history. Do NOT answer the question, \
just reformulate it if needed and otherwise return it as is. 

Chat history: {chat_history}

Latest user question: {question}

Standalone question:"""

prompts["en"][GEN] = prompts["auto"][GEN]

prompts["pl"][GEN] = """Przekształć ostatnie zdanie użytkownika, które może odwoływać się do histori rozmowy, tak aby było samodzielne, \
czyli zrozumiałe bez histori rozmowy. NIE odpowiadaj na pytanie tylko je przekształć, jeśli to konieczne, a w przeciwnym razie \
zwróć pytanie w niezmienionej postaci. 

Historia rozmowy: {chat_history}

Ostatnie pytanie użytkownika: {question}

Samodzielne pytanie:"""


prompts["da"][GEN] = """Givet en chathistorik og det seneste brugerspørgsmål som kan referere til kontekst i chathistorikken, \
formuler et selvstændigt spørgsmål som kan forstås uden chathistorikken. \
Svar IKKE på spørgsmålet, bare omformulere det, hvis det er nødvendigt, og ellers returnere det, som det er.

Chathistorik: {chat_history}

Seneste brugerspørgsmål: {question}

Enkeltstående spørgsmål:
"""

tax_qa_prompt["en"]["vestas_tech"] = """
You are a seasoned expert in wind turbine installation and maintenance with extensive hands-on experience in servicing wind turbines. 
Your task is to assist technicians working in the field by providing clear and concise answers to their questions based on the provided instruction manual for wind turbine service.

Please adhere to the following guidelines:

Answer the question using only the information from the provided context.
Use simple and straightforward language.
Respond in the same language in which the question is asked.
Context: 
{context}



Question: {question}


Date of asking this question is 1 November 2024.
Answer:
"""


def get_prompt(lang, type, tax=None):
    if tax:
        if lang not in tax_qa_prompt:
            lang = 'en'
        if tax in tax_qa_prompt[lang]:
            return tax_qa_prompt[lang][tax]

    if lang in prompts:
        return prompts[lang][type]
    return prompts['en'][type]