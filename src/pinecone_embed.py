from typing import List

from langchain_core.embeddings import Embeddings
from pinecone import Pinecone
from dotenv import load_dotenv

load_dotenv()


class PineconeEmbed(Embeddings):

    def __init__(self):
        self.pc = Pinecone()

    def embed_documents(self, texts: List[str]) -> List[List[float]]:
        res = self.pc.inference.embed(
            "multilingual-e5-large",
            inputs=texts,
            parameters={
                "input_type": "passage"
            }
        )
        return [v['values'] for v in res]

    def embed_query(self, text: str) -> List[float]:
        res = self.pc.inference.embed(
            "multilingual-e5-large",
            inputs=[text],
            parameters={
                "input_type": "query"
            }
        )
        return res[0]['values']


