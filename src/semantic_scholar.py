import logging
import os
from typing import List, Any
import requests
import arxiv
from langchain_core.callbacks import CallbackManagerForRetrieverRun
from langchain_core.messages import AIMessage
from pydantic import Field
from langchain_core.runnables import Runnable
from semanticscholar import SemanticScholar
from langchain_core.documents import Document
from langchain_core.retrievers import BaseRetriever
from PyPDF2 import PdfReader
from translate import translate

logger = logging.getLogger(__name__)

class SemanticScholarLoader():
    """
    A class to read and process data from Semantic Scholar API
    ...

    Methods:
    -------
    __init__():
       Instantiate the SemanticScholar object

    load_data(query: str, limit: int = 10, returned_fields: list = ["title", "abstract", "venue", "year", "paperId", "citationCount", "openAccessPdf", "authors"]) -> list:
        Loads data from Semantic Scholar based on the query and returned_fields

    """

    def __init__(self, timeout=100, base_dir="semanticscholar_pdfs") -> None:
        """
        Instantiate the SemanticScholar object.
        """

        self.base_dir = base_dir
        url = 'https://api.semanticscholar.org/graph/v1'
        api_key = os.getenv('SEMANTICSCHOLAR_API_KEY')
        self.s2 = SemanticScholar(timeout=timeout, api_key=api_key, api_url=url)
        # check for base dir
        if not os.path.exists(self.base_dir):
            os.makedirs(self.base_dir)

    def _download_pdf(self, paper_id, url: str, base_dir="pdfs"):
        logger = logging.getLogger()
        headers = {
            "User-Agent": (
                "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML,"
                " like Gecko) Chrome/58.0.3029.110 Safari/537.3"
            )
        }
        # Making a GET request
        response = requests.get(url, headers=headers, stream=True)
        content_type = response.headers["Content-Type"]

        # As long as the content-type is application/pdf, this will download the file
        if "application/pdf" in content_type:
            os.makedirs(base_dir, exist_ok=True)
            file_path = os.path.join(base_dir, f"{paper_id}.pdf")
            # check if the file already exists
            if os.path.exists(file_path):
                logger.info(f"{file_path} already exists")
                return file_path
            with open(file_path, "wb") as file:
                for chunk in response.iter_content(chunk_size=1024):
                    if chunk:
                        file.write(chunk)
            logger.info(f"Downloaded pdf from {url}")
            return file_path
        else:
            logger.warning(f"{url} was not downloaded: protected")
            return None

    def _update_full_text_docs(self, documents: List[Document], text_limit):
        text_count = 0
        for paper in documents:
            if text_limit - text_count < 1000:
                break

            metadata = paper.metadata
            url = metadata["source"]
            external_ids = metadata["externalIds"]
            paper_id = metadata["id"]
            file_path = os.path.join(self.base_dir, f"{paper_id}.pdf")

            if url and not os.path.exists(file_path):
                # Download the document first
                file_path = self._download_pdf(paper_id, url, self.base_dir)

            if (
                    not url
                    and external_ids
                    and "ArXiv" in external_ids
                    and not os.path.exists(file_path)
            ):
                # download the pdf from arxiv
                source, file = self._download_pdf_from_arxiv(
                    paper_id, external_ids["ArXiv"]
                )
                metadata['source'] = source
                file_path = file

            # Then, check if it's a valid PDF. If it's not, skip to the next document.
            if file_path:
                try:
                    pdf = PdfReader(open(file_path, "rb"))
                except Exception as e:
                    logging.error(
                        f"Failed to read pdf with exception: {e}. Skipping document..."
                    )
                    continue

                text = ""
                limit = text_limit - text_count
                for page in pdf.pages:
                    text += page.extract_text()
                    if len(text) >= limit:
                        continue

                if text:
                    if len(text) > limit:
                        text = text[:limit]
                    text_count += len(text)
                    paper.page_content = text

    def _download_pdf_from_arxiv(self, paper_id, arxiv_id):
        paper = next(arxiv.Search(id_list=[arxiv_id], max_results=1).results())
        file = paper.download_pdf(dirpath=self.base_dir, filename=paper_id + ".pdf")
        return paper, file

    def load_data(
            self,
            query,
            limit=5,
            text_limit=12000,  # 12000 is ~3000 tokens in english
            returned_fields=[
                "title",
                "abstract",
                "venue",
                "year",
                "paperId",
                "citationCount",
                "openAccessPdf",
                "authors",
                "externalIds",
            ],
    ) -> List[Document]:
        """
        Loads data from Semantic Scholar based on the entered query and returned_fields.

        Parameters
        ----------
        query: str
            The search query for the paper
        limit: int, optional
            The number of maximum results returned (default is 10)
        returned_fields: list, optional
            The list of fields to be returned from the search

        Returns:
        -------
        list
            The list of Document object that contains the search results

        Raises:
        ------
        Exception
            If there is an error while performing the search

        """
        try:
            results = self.s2.search_paper(query, limit=limit, fields=returned_fields)
        except (requests.HTTPError, requests.ConnectionError, requests.Timeout) as e:
            logging.error(
                "Failed to fetch data from Semantic Scholar with exception: %s", e
            )
            raise
        except Exception as e:
            logging.error("An unexpected error occurred: %s", e)
            raise

        documents = []
        text_count = 0
        for item in results[:limit]:
            id = getattr(item, "paperId", None)
            if not id:
                continue

            source = getattr(item, "openAccessPdf", None)
            abstract = getattr(item, "abstract", None)
            title = getattr(item, "title", None)
            text = None
            # concat title and abstract
            if abstract and title:
                text = title + " " + abstract
            elif not abstract:
                text = title

            if text_count + len(text) > text_limit:
                text = text[:text_limit - text_count]

            text_count += len(text)

            metadata = {
                "id": id,
                "source": source.get("url") if source else None,
                "title": title,
                "abstract": abstract,
                "venue": getattr(item, "venue", None),
                "year": getattr(item, "year", None),
                "citationCount": getattr(item, "citationCount", None),
                "authors": [author["name"] for author in getattr(item, "authors", [])],
                "externalIds": getattr(item, "externalIds", None),
            }
            documents.append(Document(page_content=text, metadata=metadata))

        # skip fulltext when no enough
        if text_limit - text_count > 1000:
            self._update_full_text_docs(documents, text_limit - text_count)

        for doc in documents:
            if not getattr(doc.metadata, 'source', None):
                doc.metadata['source'] = 'https://www.semanticscholar.org/paper/' + doc.metadata['id']

        return documents


SEMANTICSCHOLAR_PROMPT = """
Translate following user query into input for SemanticScholar.org search for scientific papers.
Use keywords from user query to form the search phrase for SemanticScholar search page.
Return only new query without any additional comments. Don't quote response.
If user query does not require translation then return original query.


For example user query: 
Write me about large language models limitations
return: 
large language models limitations

For example user query: 
Give me key points of the risks of large language models usage
return:
large language models risks

User query:
"""


class SemanticScholarRetriever(BaseRetriever):
    k_doc: int = Field(default=5)
    text_limit: int = Field(default=12000)
    llm: Runnable

    class Config:
        arbitrary_types_allowed = True

    def __init__(self, **data: Any):
        super().__init__(**data)

    def _get_relevant_documents(
            self, query: str, *, run_manager: CallbackManagerForRetrieverRun
    ) -> List[Document]:
        query_en = translate(query, 'en')
        logging.debug(f"SemanticScholar translated input query: {query} translated: {query_en}")
        prompt = SEMANTICSCHOLAR_PROMPT + query_en
        logger.info(f"PROMPT {prompt}")
        llm_out = self.llm.invoke(prompt)
        query_llm = self._extract_query_from_llm_output(llm_out, query_en)

        logging.debug(f"SemanticScholar llm_out: {llm_out} result: {query_llm}")
        logger.info(f"QUERY FROM LLM: {query_llm}")
        loader = SemanticScholarLoader()
        documents = loader.load_data(query_llm, self.k_doc, self.text_limit)
        return documents

    @staticmethod
    def _extract_query_from_llm_output(llm_out: Any, default_query: str) -> str:
        if isinstance(llm_out, str):
            return llm_out.strip()
        elif isinstance(llm_out, AIMessage):
            return llm_out.content.strip()
        return default_query.strip()
