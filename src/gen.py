from typing import Optional, List
from uuid import UUID

from langchain_core.documents import Document
from langchain_core.output_parsers import StrOutputParser
from langchain_core.prompts import ChatPromptTemplate
from langchain_core.runnables import RunnablePassthrough
from pydantic import BaseModel

import db
import llms
from gen_prompts import PROMPTS
from rag import save_question_data, get_retriever, merge_documents, limit_docs, format_docs, filter_short, tr, save_answer
from tracer import RagTracer
from translate import translate
import logging

logger = logging.getLogger(__name__)


class GenParams(BaseModel):
    question: str
    history: Optional[List] = None
    model: str
    taxonomy: str
    lang: str = "en"
    user: Optional[str] = None
    k_docs: Optional[int] = 5
    prompt: Optional[str] = None
    temperature: Optional[float] = 0
    max_tokens: Optional[int] = 512
    repetition_penalty: Optional[float] = 1
    translate_output: Optional[bool] = False
    company_id: Optional[UUID] = None


def as_data(p: GenParams):
    return {
        "prompt": p.prompt,
        "k_docs": p.k_docs,
        "temperature": p.temperature,
        "max_tokens": p.max_tokens,
        "repetition_penalty": p.repetition_penalty,
        "translate_output": p.translate_output
    }


def gen(p: GenParams, app='gen'):
    id = save_question_data(p.question, p.history, p.model, p.taxonomy, p.lang, p.user, app, as_data(p), p.company_id)
    retriever = get_retriever(p.taxonomy)
    prompt = ChatPromptTemplate.from_template(p.prompt)
    model = llms.model(p.model)['model'](temperature=p.temperature, max_tokens=p.max_tokens,
                                     repetition_penalty=p.repetition_penalty)

    def merge_k_documents(input: List[Document]):
        return merge_documents(input, k=p.k_docs)

    def question(input):
        return input['question']

    def limit_docs_context(input):
        return limit_docs(input, p.model)

    chain_docs = (
            RunnablePassthrough.assign(context=(lambda x: format_docs(x["context"])))
            | prompt
            | model
            | StrOutputParser()
    )
    chain_src = RunnablePassthrough.assign(
        context=question | retriever | filter_short | limit_docs_context | merge_k_documents | limit_docs_context
    ).assign(
        answer=chain_docs | (lambda x: x.strip())
    )

    config = {
        'callbacks': [RagTracer(id=id)],
        'model': p.model,
    }

    if p.lang not in ['en', 'pl', 'da', 'es', 'de', 'fr']:
        p.question = translate(p.question, 'en')
        db.addData(id, "question_after_translation", p.question)

    data = {"question": p.question}
    if p.history:
        data["chat_history"] = p.history

    out = chain_src.invoke(data, config=config)
    if p.translate_output:
        db.addData(id, "before_answer_translation", out['answer'])
        tr(out, p.lang)

    out['id'] = str(id)
    save_answer(id, out)
    # logger.info(f"out: {out}")
    # logger.info("-----")
    # logger.info(f"answer: {out['answer']}")
    return out


if __name__ == "__main__":
    params = GenParams(
        question="Role of blockchain in Exfluency ecosystem",
        history=[],
        model="mixtral2",
        taxonomy="rob",
        lang="en",
        user="test_user",
        prompt=PROMPTS['article']['prompt'],
        temperature=0.75,
        max_tokens=1500,
        repetition_penalty=1,
        translate_output=False
    )
    out = gen(params)

    print(out)
    print("-----")
    print(out["answer"])