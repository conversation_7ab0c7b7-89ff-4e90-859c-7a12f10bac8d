import os
from dataclasses import dataclass, field

from langchain_core.embeddings import Embeddings

from ingest.pinecone_embedding import PineconeEmbedding

INDEX = os.environ.get('INDEX')  # test, demo, main


@dataclass
class Taxonomy:
    name: str
    index: str
    embedding: Embeddings = PineconeEmbedding()


CONFLUENCE = Taxonomy(name='confluence', index='e5', embedding=PineconeEmbedding())
ADI = Taxonomy(name='3806baa8-bcc0-42bd-86e7-a2d7feda9867', index='main', embedding=PineconeEmbedding())


def taxonomy(name: str):
    if name == CONFLUENCE.name:
        return CONFLUENCE
    if name == 'adi':
        return ADI
    return Taxonomy(name=name, index=INDEX)
