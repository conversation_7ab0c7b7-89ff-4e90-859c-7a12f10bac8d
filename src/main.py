import logging
import sys
import uvicorn
from dotenv import load_dotenv
import os
import db_migrate
import db
from gelf_logging import setup_gelf_logging

load_dotenv()

setup_gelf_logging()


if __name__ == "__main__":
    db.create_schema_if_not_exists()
    db_migrate.migrate()
    # app_data folder should be mounted in docker as volume
    if not os.path.isdir('../../app_data/'):
        os.makedirs('../../app_data/')
    uvicorn.run("rest:app", host="0.0.0.0", port=int(os.environ.get("MY_PORT")), log_level="debug")