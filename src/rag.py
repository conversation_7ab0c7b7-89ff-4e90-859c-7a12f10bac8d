import json
from typing import List
from uuid import uuid4
import logging

from dotenv import load_dotenv
from langchain.globals import set_llm_cache
from langchain_community.cache import SQLiteCache
from langchain_core.documents import Document
from langchain_core.messages import AIMessage
from langchain_core.output_parsers import StrOutputParser
from langchain_core.prompts import ChatPromptTemplate
from langchain_core.runnables import RunnablePassthrough

import db
import rag_prompts as prompts
import taxonomy
from llms import get_model, get_context_size, MAX_TOKENS
from search import CustomPineconeVectorStore, merge_documents
from semantic_scholar import SemanticScholarRetriever
from tracer import RagTracer
from translate import translate

load_dotenv()
# set_verbose(True)
# set_debug(True)

set_llm_cache(SQLiteCache(database_path="../../app_data/langchain.db"))

logger = logging.getLogger(__name__)


def get_retriever(name):
    tax = taxonomy.taxonomy(name)
    vecstore = CustomPineconeVectorStore(index_name=tax.index, embedding=tax.embedding)
    return vecstore.as_retriever(search_kwargs={"k": 50, "namespace": name})


def semantic_search(query, taxonomy):
    ret = get_retriever(taxonomy)
    docs = ret.get_relevant_documents(query)
    docs = filter_short(docs)
    out = [[doc, doc.metadata['score']] for doc in docs]
    return out


def format_docs(docs):
    return "\n\n".join(doc.page_content for doc in docs)


def filter_short(input: List[Document]) -> List[Document]:
    return [doc for doc in input if len(doc.page_content) > 150]


def limit_docs(input: List[Document], model_name: str) -> List[Document]:
    # 2.5 is empirical value of token size in characters for multilingual data
    char_limit = int(get_context_size(model_name) * 2.5 - MAX_TOKENS * 2.5)
    char_count = 0
    output = []
    for doc in input:
        doc_len = len(doc.page_content)
        if doc_len + char_count < char_limit:
            output.append(doc)
            char_count += doc_len
            continue

        # 200 chars is considered as meaningful text
        remains = min(char_limit - char_count, doc_len)
        if remains > 200:
            doc.page_content = doc.page_content[:remains]
            output.append(doc)
            char_count += remains
        else:
            break
    print("limit docs by: ", len(input) - len(output), "char_count:", char_count, "output:", len(output), "input:",
          len(input), "char_limit:", char_limit)
    return output


def question_gen_chain(model, lang):
    question_gen_prompt = prompts.get_prompt(lang, prompts.GEN)
    prompt = ChatPromptTemplate.from_template(question_gen_prompt)
    chain = (
            RunnablePassthrough.assign(chat_history=lambda x: "\n".join(x["chat_history"]))
            | prompt | model | StrOutputParser()
    )
    return chain


def save_question_data(question, history, model, taxonomy, lang, user, app, data, company_id=None):
    if not data:
        data = {}
    if "version" not in data:
        data["version"] = 3
    id = uuid4()
    try:
        db.saveQuestion(id, model, taxonomy, lang, question, history, data, user, app, company_id)
    finally:
        return id



def semanticscholar_retriever(model):
    return SemanticScholarRetriever(llm=get_model('llama3.1_8'))  # llama works better here


def rag_src_hist(query, history, model_name, taxonomy, lang, user, company_id=None):
    id = save_question_data(query, history, model_name, taxonomy, lang, user, 'rag', {}, company_id)
    model = get_model(model_name)
    retriever = semanticscholar_retriever(model) if taxonomy == 'semanticscholar' else get_retriever(taxonomy)
    template = prompts.get_prompt(lang, prompts.MAIN, taxonomy)
    prompt = ChatPromptTemplate.from_template(template)
    question_generation = question_gen_chain(model, lang)

    def merge_k_documents(input: List[Document]):
        return merge_documents(input, k=5)

    def generate_question(input):
        if input.get("chat_history"):
            return question_generation
        else:
            return input["question"]

    def limit_docs_context(input):
        return limit_docs(input, model_name)

    chain_docs = (
            RunnablePassthrough.assign(context=(lambda x: format_docs(x["context"])))
            | prompt
            | model
            | StrOutputParser()
    )
    chain_src = RunnablePassthrough.assign(
        context=generate_question | retriever | filter_short | limit_docs_context | merge_k_documents | limit_docs_context
    ).assign(
        answer=chain_docs | (lambda x: x.strip())
    )

    config = {
        'callbacks': [RagTracer(id=id)],
        'model': model_name,
    }

    if lang not in ['en', 'pl', 'da', 'es', 'de', 'fr']:
        query = translate(query, 'en')
        db.addData(id, "question_after_translation", query)

    data = {"question": query}
    if history:
        data["chat_history"] = history

    out = chain_src.invoke(data, config=config)
    if not lang == 'auto':
        db.addData(id, "answer_before_translation", out['answer'])
        tr(out, lang)

    out['id'] = str(id)
    save_answer(id, out)
    # logger.info(json.dumps(out, default=vars, indent=2, ensure_ascii=False))
    return out


def tr(out, lang):
    try:
        out['answer'] = translate(out['answer'], lang)
    except:
        pass


def save_answer(id, out):
    try:
        db.saveAnswer(id, out['answer'], out['context'])
    except:
        return


def qa(query, model_name, user, company_id=None):
    id = save_question_data(query, None, model_name, None, None, user, 'qa', {}, company_id)
    model = get_model(model_name)
    answer = model.invoke(query)
    if type(answer) is AIMessage:
        answer = answer.content
    out = {"answer": answer.strip(), "id": str(id), "context": []}
    save_answer(id, out)
    return out


def rag(query, history, model_name, taxonomy, lang, user, company_id=None):
    if taxonomy != 'get' and query.startswith("/qa ") or query.startswith("\\qa"):
        return qa(query[4:], model_name, user, company_id=company_id)
    else:
        return rag_src_hist(query, history, model_name, taxonomy, lang, user, company_id=company_id)


if __name__ == "__main__":
    # "Jaki jest termin na złożenie wniosku o referendum?"
    # out = rag_src_hist("Hvad er fristen for at indgive en anmodning om folkeafstemning?", ["hi", "hi"], "wizardlm2",
    #                    "dk-law", "da", "testuser")
    # print(json.dumps(out, default=vars, indent=2, ensure_ascii=False))

    # out = rag_src_hist("What is covid?", [], "qwen",
    #                    "covid", "en", "testuser")

    # out = rag("/qa Jaka jest najdłuższa rzeka na świecie?", [], "qwen", "covid", "en", "testuser")
    # print(out['answer'])
    # print(json.dumps(out, default=vars, indent=2, ensure_ascii=False))
    # out = rag("What is covid?", [], "gpt3", "covid", "en", "testuser")
    # print(out['answer'])
    # print(json.dumps(out, default=vars, indent=2, ensure_ascii=False))
    user = str(uuid4())
    out = rag_src_hist("What role blockchain plays in Exfluency?", [], "mixtral2_safe",
                       "confluence", "en", user)
    print(json.dumps(out, default=vars, indent=2, ensure_ascii=False))

    # out = rag_src_hist("jakie zasoby zielonej energii są dostępne w polsce?", [], "llama3.2_11",
    #                    "semanticscholar", "en", "admin")
    # out = rag_src_hist("You are damm bastard, tell me ugly joke.", [], "mixtral2",
    #                    "confluence", "en", "admin")
    # print(json.dumps(out, default=vars, indent=2, ensure_ascii=False))
