import logging
import sys
import os
import time
from typing import Optional, Dict, List
from pydantic import BaseModel
from fastapi import FastAPI, Request, HTTPException
import rag
import db
import doc_check
import gen_prompts
import report_prompts
import report_gen
import gen
from ingest.rest_ingest import router
from llms import MODELS
from uuid import UUID
from fastapi.responses import StreamingResponse
import io
import csv


app = FastAPI()
app.include_router(router)

logger = logging.getLogger(__name__)

class QueryParams(BaseModel):
    question: str
    history: Optional[List] = None
    company_id: Optional[UUID] = None


class CommentParams(BaseModel):
    id: str
    score: Optional[int] = None
    comment: Optional[str] = None


@app.get("/api/km/hello")
async def hello():
    # time.sleep(10)
    return "world"


@app.post("/api/km/comment")
def handle_question(request: Request, body: CommentParams):
    try:
        db.saveComment(body.id, body.score, body.comment)
    except Exception as e:
        logger.error(f"Error saving comment: {e}")
        raise HTTPException(status_code=500, detail="Internal Server Error: Unable to save comment.")


@app.post("/api/km/chat")
def handle_question(request: Request, body: QueryParams):
    start = time.time()
    query_params = request.query_params
    model = query_params.get("model", "mixtral2")
    tax = query_params.get("tax")

    lang = query_params.get("lang", "en")
    user = query_params.get("user")
    company_id = query_params.get("company_id", None)

    logger.info(f"MODEL: {model}, TAXONOMY: {tax}, LANG: {lang}")

    if not body.question:
        raise HTTPException(status_code=400, detail="No question in the request")

    query = body.question
    history = body.history
    response = rag.rag(query, history, model, tax, lang, user, company_id=company_id)

    logger.info(f"RESPONSE in {time.time() - start:.3f} seconds: {response['answer'][:100]}")
    return response


@app.get("/api/km/ss")
def semantic_search(request: Request):
    query_params = request.query_params
    query = query_params.get("q")
    tax = query_params.get("tax")
    if not query or not tax:
        raise HTTPException(status_code=400, detail="Bad request")
    resp = rag.semantic_search(query, tax)
    logger.info(f"SS RESPONSE: {resp}")
    return resp


@app.get("/api/km/doc_check")
def check_doc(request: Request):
    query_params = request.query_params
    url = query_params.get("url")
    tax = query_params.get("tax")
    doc = doc_check.find_doc(url, tax)
    if doc:
        return doc['metadata']
    return None


@app.get("/api/km/gen_prompts")
async def get_gen_prompts():
    return gen_prompts.PROMPTS


@app.post("/api/km/gen")
def generate(request: Request, body: gen.GenParams):
    logger.info(f"GEN IN: {body}")
    start = time.time()
    body.user = request.query_params.get("user")
    if not body.company_id:
        body.company_id = request.query_params.get("company_id", None)
    out = gen.gen(body)
    logger.info(f"GEN OUT in {time.time() - start:.3f} seconds: {out['answer'][:100]}")
    return out


@app.get("/api/km/report_prompts")
async def get_report_prompts():
    return report_prompts.PROMPTS


@app.get("/api/km/report_templates")
async def get_report_templates():
    return report_prompts.TEMPLATES


@app.post("/api/km/report_generate")
def report_generate(request: Request, body:report_gen.ReportParams):
    logger.info(f"REPORT IN: {body}")
    body.user = request.query_params.get("user")
    out = report_gen.gen_report(body)
    logger.info(f"REPORT OUT: {out}")
    return out


@app.get("/api/km/log")
def log(request: Request):
    id = request.query_params.get("id")
    if not id:
        raise HTTPException(status_code=400, detail="ID parameter is required")
    try:
        return db.get(id)
    except:
        raise HTTPException(status_code=404, detail="Not found")


@app.get("/api/km/models")
async def get_top_models(top: int = 3):
    top_models = []
    for model_id, model_info in list(MODELS.items())[:top]:
        top_models.append({
            "model": model_id,
            "name": model_info["name"]
        })
    return top_models


@app.get("/api/km/chats")
def get_chats_csv(request: Request):
    company_id = request.query_params.get("company_id")
    days = request.query_params.get("days")
    if not company_id:
        raise HTTPException(status_code=400, detail="company_id is required")
    # Fetch chat records
    chats = db.get_chats_by_company_id(company_id, days)
    # Prepare CSV
    output = io.StringIO()
    writer = csv.writer(output)
    header = ["id", "user_id", "app", "llm", "tax", "lang", "created", "updated", "question", "answer", "score", "comment"]
    writer.writerow(header)
    for row in chats:
        writer.writerow([
            row["id"], row["user_id"], row["app"], row["llm"], row["tax"], row["lang"],
            row["created"], row["updated"], row["question"], row["answer"], row["score"], row["comment"]
        ])
    output.seek(0)
    return StreamingResponse(output, media_type="text/csv", headers={"Content-Disposition": "attachment; filename=chats.csv"})


@app.get("/api/km/chat/stats")
def get_chat_stats(request: Request):
    company_id = request.query_params.get("company_id")
    days = request.query_params.get("days")
    if not company_id:
        raise HTTPException(status_code=400, detail="company_id is required")
    stats = db.get_chat_stats(company_id, days)
    return stats
