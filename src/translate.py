import os
import functools
import requests
from eld import LanguageDetector
import time
import logging

# import dotenv
# dotenv.load_dotenv()

EXFLUENCY_TRANSLATOR_URL = os.environ.get("EXFLUENCY_TRANSLATOR_URL", None)
detector = LanguageDetector()
logger = logging.getLogger(__name__)


def translate(text, toLang, fromLang=None):
    lang = _detect_language(text)
    if lang:
        if lang.upper() == toLang.upper():
            return text
        if lang.upper() == 'EN' and (toLang.upper() == 'EN_US' or toLang.upper() == 'EN_GB'):
            return text
        if lang.upper() == 'ZH' and toLang.upper() == 'ZH_HANS':
            return text
    out = None
    if EXFLUENCY_TRANSLATOR_URL:
        out = _translate_exfluency(text, toLang, fromLang)
    if not out:
        return text
    return out


def _detect_language(text):
    # https://github.com/nitotm/efficient-language-detector-py
    return detector.detect(text).language


def _translate_exfluency(text, toLang, fromLang=None):
    if not fromLang:
        fromLang = _detect_language(text)
    if not fromLang:
        fromLang = 'EN_US'
    if fromLang == 'zh':
        fromLang = 'zh_hans'
    try:
        start_time = time.time()
        response = requests.post(
            EXFLUENCY_TRANSLATOR_URL,
            headers={"Content-Type": "application/json; charset=UTF-8"},
            json={"text": text, "langFrom": fromLang.upper(), "langTo": toLang.upper()}
        )
        elapsed = time.time() - start_time
        logger.info(f"[Timing] Exfluency remote call took {elapsed:.3f} seconds")
        response.raise_for_status()
        result = response.json()
        if result and isinstance(result, list) and len(result) > 0:
            return result[0]["targetText"]
        raise Exception("Invalid response format from Exfluency translator")
    except Exception as e:
        logger.error(f"Exfluency translator error: {str(e)}")
        return None

if __name__ == "__main__":
    logger.info(f"_detect_language: {_detect_language('Hvad er fristen for at indgive en anmodning om folkeafstemning?')}")
    logger.info(f"_detect_language: {_detect_language('adkjfapiceradaera adfarea adfa?')}")
    logger.info(f"_detect_language: {_detect_language('145141354154145141?')}")
    t = translate('Hvad er fristen for at indgive en anmodning om folkeafstemning?', 'pl')
    logger.info(f"t: {t}")
