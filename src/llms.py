from typing import Any

from langchain_groq import <PERSON>tGro<PERSON>
from langchain_openai import OpenAIEmbeddings, ChatOpenAI
from langchain_together import Together, ChatTogether

MAX_TOKENS = 2048


class TogetherWithDict(Together):
    # fix the caching
    def dict(self, **kwargs: Any) -> dict:
        return {
            "type": self._llm_type,
            "model": self.model,
            "temperature": self.temperature,
            "max_tokens": self.max_tokens,
            "repetition_penalty": self.repetition_penalty
        }


class TogetherWithDictSafe(Together):
    safety_model: str = "meta-llama/Meta-Llama-Guard-3-8B"
    # fix the caching
    def dict(self, **kwargs: Any) -> dict:
        return {
            "type": self._llm_type,
            "model": self.model,
            "temperature": self.temperature,
            "max_tokens": self.max_tokens,
            "repetition_penalty": self.repetition_penalty,
            "safety_model": self.safety_model
        }

# Define factory functions with repetition_penalty
def create_together_model(model, temperature=0, max_tokens=MAX_TOKENS, repetition_penalty=1):
    return TogetherWithDict(model=model, temperature=temperature, max_tokens=max_tokens, repetition_penalty=repetition_penalty)

def create_together_model_safe(model, temperature=0, max_tokens=MAX_TOKENS, repetition_penalty=1):
    return TogetherWithDictSafe(model=model, temperature=temperature, max_tokens=max_tokens, repetition_penalty=repetition_penalty, safety_model="meta-llama/Meta-Llama-Guard-3-8B")

def create_groq_model(model_name, temperature=0, max_tokens=MAX_TOKENS, repetition_penalty=1):
    return ChatGroq(model_name=model_name, temperature=temperature, max_tokens=max_tokens)


def create_chat_openai_model(model, temperature=0, max_tokens=MAX_TOKENS, repetition_penalty=1):
    return ChatOpenAI(model=model, temperature=temperature, max_tokens=max_tokens)


def create_chat_together(model, temperature=0, max_tokens=MAX_TOKENS, repetition_penalty=1):
    return ChatTogether(model=model, temperature=temperature, max_tokens=max_tokens)


# Define the MODELS dictionary using factory methods
MODELS = {
    "mistral3": {
        "model": lambda temperature=0, max_tokens=MAX_TOKENS, repetition_penalty=1: create_chat_together(
            "mistralai/Mistral-Small-24B-Instruct-2501", temperature, max_tokens, repetition_penalty),
        "context": 128000,
        "name": "Mistral"
    },
    "llama3.1_70b_nemo": {
        "model": lambda temperature=0, max_tokens=MAX_TOKENS, repetition_penalty=1: create_chat_together(
            "nvidia/Llama-3.1-Nemotron-70B-Instruct-HF", temperature, max_tokens, repetition_penalty),
        "context": 32000,
        "name": "Llama"
    },
    "kimi2": {
        "model": lambda temperature=0, max_tokens=MAX_TOKENS, repetition_penalty=1: create_chat_together(
            "moonshotai/Kimi-K2-Instruct", temperature, max_tokens, repetition_penalty),
        "context": 128000,
        "name": "Kimi K2 (beta)"
    },
    "deepseek3": {
        "model": lambda temperature=0, max_tokens=MAX_TOKENS, repetition_penalty=1: create_chat_together(
            "deepseek-ai/DeepSeek-V3", temperature, max_tokens, repetition_penalty),
        "context": 128000,
        "name": "DeepSeek"
    },
    "llama3.3_70": {
        "model": lambda temperature=0, max_tokens=MAX_TOKENS, repetition_penalty=1: create_chat_together(
            "meta-llama/Llama-3.3-70B-Instruct-Turbo", temperature, max_tokens, repetition_penalty),
        "context": 128000,
        "name": "Llama 3.3 70B"
    },
    "llama4": {
        "model": lambda temperature=0, max_tokens=MAX_TOKENS, repetition_penalty=1: create_chat_together("meta-llama/Llama-4-Maverick-17B-128E-Instruct-FP8", temperature, max_tokens, repetition_penalty),
        "context": 128000,
        "name": "Llama 4"
    },
    "qwen_32_qwq": {
        "model": lambda temperature=0, max_tokens=MAX_TOKENS, repetition_penalty=1: create_chat_together("Qwen/QwQ-32B-Preview", temperature, max_tokens, repetition_penalty),
        "context": 32000,
        "name": "Qwen 32B QwQ"
    },

    "mixtral2_safe": {
        "model": lambda temperature=0, max_tokens=MAX_TOKENS, repetition_penalty=1: create_together_model_safe( "mistralai/Mixtral-8x22B-Instruct-v0.1", temperature, max_tokens, repetition_penalty),
        "context": 65000,
        "name": "Mixtral 2 Safe"
    },
    "mixtral2": {
        "model": lambda temperature=0, max_tokens=MAX_TOKENS, repetition_penalty=1: create_together_model("mistralai/Mixtral-8x22B-Instruct-v0.1", temperature, max_tokens, repetition_penalty),
        "context": 65000,
        "name": "Mixtral 2"
    },
    "mixtral": {
        "model": lambda temperature=0, max_tokens=MAX_TOKENS, repetition_penalty=1: create_together_model("mistralai/Mixtral-8x7B-Instruct-v0.1", temperature, max_tokens, repetition_penalty),
        "context": 32000,
        "name": "Mixtral"
    },
    "llama3": {
        "model": lambda temperature=0, max_tokens=MAX_TOKENS, repetition_penalty=1: create_groq_model("llama3-70b-8192", temperature, max_tokens, repetition_penalty),
        "context": 8000,
        "name": "Llama 3"
    },
    "llama3.1_8": {
        "model": lambda temperature=0, max_tokens=MAX_TOKENS, repetition_penalty=1: create_groq_model("llama-3.1-8b-instant", temperature, max_tokens, repetition_penalty),
        "context": 8000,
        "name": "Llama 3.1 8B"
    },
    "llama3.2_11g": {
        "model": lambda temperature=0, max_tokens=MAX_TOKENS, repetition_penalty=1: create_groq_model("llama-3.2-11b-vision-preview", temperature, max_tokens, repetition_penalty),
        "context": 128000,
        "name": "Llama 3.2 11B Vision"
    },
    "llama3.2_11": {
        "model": lambda temperature=0, max_tokens=MAX_TOKENS, repetition_penalty=1: create_chat_together("meta-llama/Llama-3.2-11B-Vision-Instruct-Turbo", temperature, max_tokens, repetition_penalty),
        "context": 128000,
        "name": "Llama 3.2 11B"
    },
    "llama3.2_90": {
        "model": lambda temperature=0, max_tokens=MAX_TOKENS, repetition_penalty=1: create_chat_together("meta-llama/Llama-3.2-90B-Vision-Instruct-Turbo", temperature, max_tokens, repetition_penalty),
        "context": 128000,
        "name": "Llama 3.2 90B"
    },
    "llama3.2_3": {
        "model": lambda temperature=0, max_tokens=MAX_TOKENS, repetition_penalty=1: create_chat_together("meta-llama/Llama-3.2-3B-Instruct-Turbo", temperature, max_tokens, repetition_penalty),
        "context": 128000,
        "name": "Llama 3.2 3B"
    },
    "qwen2.5_7": {
        "model": lambda temperature=0, max_tokens=MAX_TOKENS, repetition_penalty=1: create_chat_together("Qwen/Qwen2.5-7B-Instruct-Turbo", temperature, max_tokens, repetition_penalty),
        "context": 128000,
        "name": "Qwen 2.5 7B"
    },
    "qwen2.5_72": {
        "model": lambda temperature=0, max_tokens=MAX_TOKENS, repetition_penalty=1: create_chat_together("Qwen/Qwen2.5-7B-Instruct-Turbo", temperature, max_tokens, repetition_penalty),
        "context": 128000,
        "name": "Qwen 2.5 72B"
    },
    "llama2": {
        "model": lambda temperature=0, max_tokens=MAX_TOKENS, repetition_penalty=1: create_together_model("meta-llama/Llama-2-70b-chat-hf", temperature, max_tokens, repetition_penalty),
        "context": 4000,
        "name": "Llama 2"
    },
    "wizardlm2": {
        "model": lambda temperature=0, max_tokens=MAX_TOKENS, repetition_penalty=1: create_together_model("microsoft/WizardLM-2-8x22B", temperature, max_tokens, repetition_penalty),
        "context": 32000,
        "name": "WizardLM 2"
    },
    "qwen2": {
        "model": lambda temperature=0, max_tokens=MAX_TOKENS, repetition_penalty=1: create_together_model(
            "Qwen/Qwen2-72B-Instruct", temperature, max_tokens, repetition_penalty),
        "context": 32000,
        "name": "Qwen 2"
    },
    "qwen": {
        "model": lambda temperature=0, max_tokens=MAX_TOKENS, repetition_penalty=1: create_together_model("Qwen/Qwen1.5-72B-Chat", temperature, max_tokens, repetition_penalty),
        "context": 4000,
        "name": "Qwen"
    },
    "gpt3": {
        "model": lambda temperature=0, max_tokens=MAX_TOKENS, repetition_penalty=1: create_chat_openai_model("gpt-3.5-turbo-16k", temperature, max_tokens, repetition_penalty),
        "context": 16000,
        "name": "GPT-3"
    },
    "gpt4": {
        "model": lambda temperature=0, max_tokens=MAX_TOKENS, repetition_penalty=1: create_chat_openai_model("gpt-4-turbo-preview", temperature, max_tokens, repetition_penalty),
        "context": 128000,  # 1 full requests cost $1.3
        "name": "GPT-4"
    },
    "gpt4o": {
        "model": lambda temperature=0, max_tokens=MAX_TOKENS, repetition_penalty=1: create_chat_openai_model("gpt-4o", temperature, max_tokens, repetition_penalty),
        "context": 128000,
        "name": "GPT-4O"
    }
}

def model(name):
    return MODELS[name]

def get_model(name):
    return MODELS[name]['model']()

def get_context_size(name):
    return MODELS[name]['context']