"""
GELF logging configuration for sending logs to Logstash.
"""

import os
import logging
import logging.config
from typing import Optional
from pygelf import GelfUdpHandler, GelfTcpHandler
import json
from functools import wraps
import inspect


class GelfExtraFilter(logging.Filter):
    """Filter to add extra fields to GELF messages."""
    
    def __init__(self, facility: str = "spellchecker-api"):
        super().__init__()
        self.facility = facility
    
    def filter(self, record):
        # Add timestamp in the requested format
        record.timestamp = self.formatTime(record, "%Y-%m-%d %H:%M:%S.%f")[:-3]
        
        # Add logger, level, method information
        record.logger = record.name
        record.level = record.levelname
        
        # Try to get method name from the stack
        if hasattr(record, 'funcName'):
            record.method = record.funcName
        else:
            record.method = "unknown"
        
        # Add message
        if hasattr(record, 'getMessage'):
            record.message = record.getMessage()
        else:
            record.message = record.msg
        
        # Add exception information if available
        if record.exc_info:
            record.exception = self.formatException(record.exc_info)
        else:
            record.exception = None
        
        # Add userId if available in the record 
        if not hasattr(record, 'userId'):
            record.userId = None
        
        # Add facility field
        record.facility = self.facility
        
        return True
    
    def formatTime(self, record, datefmt=None):
        """Format the time for the record."""
        from datetime import datetime
        if datefmt:
            return datetime.fromtimestamp(record.created).strftime(datefmt)
        return datetime.fromtimestamp(record.created).isoformat()
    
    def formatException(self, ei):
        """Format exception info as string."""
        import traceback
        return ''.join(traceback.format_exception(*ei))


def setup_gelf_logging(
    logstash_host: Optional[str] = None,
    logstash_port: Optional[int] = None,
    facility: str = "knowledge-mining-api",
    level: Optional[str] = None
) -> None:
    """
    Set up GELF logging to send logs to Logstash.
    
    Args:
        logstash_host: Logstash host (default from LOGSTASH_HOST env var)
        logstash_port: Logstash port (default from LOGSTASH_PORT env var)
        facility: Facility name for GELF messages
        level: Logging level (default from LOG_LEVEL env var or INFO)
    """
    
    # Get configuration from environment variables
    if logstash_host is None:
        logstash_host = os.getenv("LOGSTASH_HOST", "127.0.0.1")
    
    if logstash_port is None:
        logstash_port = int(os.getenv("LOGSTASH_PORT", "5000"))
    
    if level is None:
        level = os.getenv("LOG_LEVEL", "INFO")
    
    # Get the root logger
    root_logger = logging.getLogger()
    
    # Clear existing handlers
    root_logger.handlers.clear()
    
    # Set up console handler (keep existing console output)
    console_handler = logging.StreamHandler()
    console_formatter = logging.Formatter(
        '%(asctime)s - %(name)s - %(levelname)s - %(message)s'
    )
    console_handler.setFormatter(console_formatter)
    
    try:
        # Set up GELF handler
        gelf_handler = GelfUdpHandler(
            host=logstash_host,
            port=logstash_port,
            facility=facility,
            version='1.1',
            debug=False,
            include_extra_fields=True
        )
        
        # Add custom filter to GELF handler
        gelf_filter = GelfExtraFilter(facility)
        gelf_handler.addFilter(gelf_filter)
        
        # Add both handlers to root logger
        root_logger.addHandler(console_handler)
        root_logger.addHandler(gelf_handler)
        
        # Set logging level
        root_logger.setLevel(getattr(logging, level.upper()))
        
        # Configure specific loggers to reduce noise
        logging.getLogger("httpx").setLevel(logging.WARNING)
        logging.getLogger("uvicorn.access").setLevel(logging.WARNING)
        
        # Log that GELF is configured
        logger = logging.getLogger(__name__)
        logger.info(f"✅ GELF logging configured - sending to {logstash_host}:{logstash_port} (facility: {facility})")
        
        # Send a test message to verify connectivity
        test_logger = logging.getLogger("gelf_test")
        test_logger.info("🧪 GELF connectivity test message")
        
    except Exception as e:
        # If GELF setup fails, just use console logging
        root_logger.addHandler(console_handler)
        root_logger.setLevel(getattr(logging, level.upper()))
        
        logger = logging.getLogger(__name__)
        logger.error(f"❌ GELF logging setup failed: {e}")
        logger.warning(f"⚠️ Falling back to console logging only")
        logger.info(f"🔧 To fix: Check LOGSTASH_HOST ({logstash_host}) and LOGSTASH_PORT ({logstash_port})")
        
        # Configure specific loggers to reduce noise
        logging.getLogger("httpx").setLevel(logging.WARNING)
        logging.getLogger("uvicorn.access").setLevel(logging.WARNING)

def get_logger_with_user_id(name: str, user_id: Optional[str] = None) -> logging.Logger:
    """
    Get a logger with user_id automatically added to all log messages.
    
    Args:
        name: Logger name
        user_id: User ID to add to log messages
    
    Returns:
        Logger with user_id filter applied
    """
    logger = logging.getLogger(name)
    
    if user_id:
        class UserIdFilter(logging.Filter):
            def filter(self, record):
                record.userId = user_id
                return True
        
        user_filter = UserIdFilter()
        logger.addFilter(user_filter)
    
    return logger 