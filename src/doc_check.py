import re
from urllib.parse import urlparse

from dotenv import load_dotenv
from pinecone import Pinecone
from taxonomy import taxonomy
import hashlib

load_dotenv()


def hash_text(text):
    return hashlib.sha256(text.encode("utf-8")).hexdigest()


def load_pinecone(tax):
    pc = Pinecone()
    pc_index = pc.Index(taxonomy(tax).index)
    return pc_index


def find_doc(url: str, tax: str):
    id = parse_id(url, tax)
    if id:
        pci = load_pinecone(tax)
        doc = pci.fetch([id], namespace=tax)
        vectors = doc['vectors']
        if id in vectors:
            return vectors[id]
    return None


def parse_id(url, tax):
    if tax == 'dk-law':
        parsed = urlparse(url)
        path = parsed.path
        return path + "#0"
    elif tax == 'exfluency' or tax == 'exfluencyecosystem':
        # extract 10444711048465 from  'https://exfluency.zendesk.com/hc/en-gb/articles/10444711048465-What-happens-to-Trust-Miner-feedback'
        match = re.search(r'/(\d+)-', url)
        if match:
            return match.group(1)
    elif tax == 'confluence':
        # extract number ********* after pages/  from url https://exfluency.atlassian.net/wiki/spaces/EDS/pages/*********/Selection+Algorithm
        match = re.search(r'/pages/(\d+)/', url)
        if match:
            return match.group(1) + "#0"
    elif tax == 'vestas':
        return hash_text(url) + '#0'
    return None

