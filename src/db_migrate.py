from pyway.migrate import Migrate
from pyway.settings import ConfigFile
import os
import logging

logger = logging.getLogger(__name__)

def migrate():
    config = ConfigFile()
    config.database_type = "postgres"
    config.database_host = os.environ.get("DB_HOST")
    config.database_username = os.environ.get("DB_USER")
    config.database_password = os.environ.get("DB_PASS")
    config.database_port = os.environ.get("DB_PORT")
    config.database_name = os.environ.get("DB_NAME")
    config.database_table = 'km.pyway'
    config.database_migration_dir = "db"
    output = Migrate(config).run()
    logger.info(f"pyway: {output}")
