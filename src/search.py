import copy
from typing import Optional, List, Any

from langchain_core.documents import Document
from langchain_pinecone import PineconeVectorStore


class CustomPineconeVectorStore(PineconeVectorStore):

    def similarity_search(
            self,
            query: str,
            k: int = 4,
            filter: Optional[dict] = None,
            namespace: Optional[str] = None,
            **kwargs: Any,
    ) -> List[Document]:
        """Return pinecone documents most similar to query.
           Change is to add score to Document metadata

        Args:
            query: Text to look up documents similar to.
            k: Number of Documents to return. Defaults to 4.
            filter: Dictionary of argument(s) to filter on metadata
            namespace: Namespace to search in. <PERSON><PERSON><PERSON> will search in '' namespace.

        Returns:
            List of Documents most similar to the query and score for each
        """
        docs_and_scores = self.similarity_search_with_score(
            query, k=k, filter=filter, namespace=namespace, **kwargs
        )
        docs = []
        for doc, score in docs_and_scores:
            doc.metadata['score'] = score
            docs.append(doc)
        return docs


def merge_documents(input: List[Document], k=3) -> List[Document]:
    map = {}
    cut = []
    # group and cut
    for doc in input:
        id = doc.metadata['source']
        if id in map:
            map[id].append(doc)
        elif len(cut) < k:
            map[id] = [doc]
            cut.append(id)

    out = []
    for id, docs in map.items():
        doc = _merge_docs(docs)
        _add_metadata(doc)
        out.append(doc)

    return out


META_KEYS = ["title", "Offentliggørelsesdato", "shortName", "Dokumenttype", "ExternalId", "source", "År for udstedelse",
             "Accession"]


def _add_metadata(doc: Document):
    txt = "<metadata>"
    for k in META_KEYS:
        if k in doc.metadata:
            txt += f"{k}: {doc.metadata[k]}, "
    txt = txt[:-2]
    txt += "</metadata>"
    doc.page_content = txt + "\n" + doc.page_content


def _merge_docs(input: List[Document]) -> Document:
    metadata = copy.deepcopy(input[0].metadata)
    metadata['merged_parts'] = []
    scores = []
    content = ""
    input.sort(key=lambda x: x.metadata.get('part', 0))
    for doc in input:
        if 'score' in metadata:
            scores.append(doc.metadata['score'])
        metadata['merged_parts'].append(doc.metadata.get('part', 0))
        content += "\n\n" + doc.page_content

    if scores:
        metadata['score'] = sum(scores) / len(scores)
    return Document(page_content=content, metadata=metadata)
