from typing import Any
from uuid import UUID
import logging

from langchain_core.tracers.base import BaseTracer
from langchain_core.tracers.schemas import Run
import db
import time

logger = logging.getLogger(__name__)

class RagTracer(BaseTracer):

    def __init__(self, *, id: UUID, **kwargs: Any) -> None:
        super().__init__(**kwargs)
        self.rag_id = id

    def _persist_run(self, run: Run) -> None:
        pass

    def _on_llm_start(self, run: Run) -> None:
        self.llm_start_time = time.time()
        logger.info(f"llm_start | rag_id={self.rag_id} | run={self._tostr(run)}")

    def _on_llm_end(self, run: Run) -> None:
        try:
            db.addDataToList(self.rag_id, "llm", dict(run))
        except Exception as e:
            logger.error(f"db addData llm failed | error={e} | run={run}")
        elapsed = time.time() - self.llm_start_time
        logger.info(f"llm_end in {elapsed:.3f} seconds | rag_id={self.rag_id} | run={self._tostr(run)}")

    def _on_llm_error(self, run: Run) -> None:
        logger.error(f"llm_error | rag_id={self.rag_id} | run={run}")

    def _on_chain_start(self, run: Run) -> None:
        """Process the Chain Run upon start."""

    def _on_chain_end(self, run: Run) -> None:
        """Process the Chain Run."""

    def _on_chain_error(self, run: Run) -> None:
        """Process the Chain Run upon error."""

    def _on_chat_model_start(self, run: Run) -> None:
        logger.info(f"chat_model_start | rag_id={self.rag_id} | run={self._tostr(run)}")

    def _on_retriever_start(self, run: Run) -> None:
        self.retriever_start_time = time.time()
        logger.info(f"retriever_start | rag_id={self.rag_id} | run={self._tostr(run)}")

    def _on_retriever_end(self, run: Run) -> None:
        elapsed = time.time() - self.retriever_start_time
        logger.info(f"retriever_end in {elapsed:.3f} seconds | rag_id={self.rag_id} | run={self._tostr(run)}")

    def _on_retriever_error(self, run: Run) -> None:
        logger.error(f"retriever_error | rag_id={self.rag_id} | run={run}")

    def _tostr(self, data):
        if not data:
            return ""
        return str(data)[:100]