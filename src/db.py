from sqlalchemy import create_engine, text
import os
import dotenv
import json
import logging

dotenv.load_dotenv()
DB_URL = f"postgresql://{os.environ.get('DB_USER')}:{os.environ.get('DB_PASS')}@{os.environ.get('DB_HOST')}:{os.environ.get('DB_PORT')}/{os.environ.get('DB_NAME')}"

# Default engine uses 'km' schema
engine = create_engine(f"{DB_URL}?options=-c search_path=km", echo=False)

logger = logging.getLogger(__name__)

def create_schema_if_not_exists():
    # Create a temporary engine with default schema for creating 'km' schema
    default_engine = create_engine(DB_URL, echo=False)
    # Use default_engine to ensure we're in the default schema when creating 'km'
    with default_engine.connect() as conn:
        conn.execute(text("CREATE SCHEMA IF NOT EXISTS km"))
        conn.commit()

    default_engine.dispose()


def get(id):
    query = "select * from km.chat where id = :id"
    data = {"id": id}
    return select(query, data).first()


def select(query: str, data: dict = None):
    with engine.connect() as conn:
        if data:
            result = conn.execute(text(query), data)
        else:
            result = conn.execute(text(query))
        return result.mappings()


def execute(query: str, data: dict):
    with engine.connect() as conn:
        trans = conn.begin()
        try:
            conn.execute(text(query), data)
            trans.commit()
        except Exception as e:
            trans.rollback()
            logger.error(f"Execute error: {e}")
            raise


def jsonstr(data):
    if data:
        return json.dumps(data, default=vars, ensure_ascii=False)
    return data


def saveQuestion(id, model, tax, lang, question, history, data, user, app, company_id=None):
    query = """
        INSERT INTO km.chat (id, llm, tax, lang, question, history, data, user_id, app, company_id)
        VALUES (:id, :llm, :tax, :lang, :question, :history, :data, :user_id, :app, :company_id)
    """
    dat = {
        'id': id,
        'llm': model,
        'tax': tax,
        'lang': lang,
        'question': question,
        'history': jsonstr(history),
        'data': jsonstr(data),
        'user_id': user,
        'app': app,
        'company_id': company_id
    }
    execute(query, dat)


def saveAnswer(id, answer, sources):
    query = """
        UPDATE km.chat SET answer = :answer, sources = :sources, updated = NOW() WHERE id = :id
    """
    data = {
        "id": id,
        "answer": answer,
        "sources": jsonstr(sources)
    }
    execute(query, data)


def saveComment(id, score, comment):
    query = """
        UPDATE km.chat SET score = :score, comment = :comment, updated = NOW() WHERE id = :id
    """
    data = {
        "id": id,
        "score": int(score),
        "comment": comment
    }
    execute(query, data)


def addData(id, key, value):
    value_json = json.dumps(value, default=str)
    query = "UPDATE km.chat SET data = jsonb_set(data, '{"+key+"}', :value, true ), updated = NOW() WHERE id = :id"
    data = {"id": id, "value": value_json}
    execute(query, data)


def addDataToList(id, key, value):
    value_json = json.dumps([value], default=str)
    query = """
    UPDATE km.chat
    SET data = CASE
                   WHEN data->'""" + key + """' IS NULL THEN jsonb_set(data, '{""" + key + """}', to_jsonb(:value ::jsonb), true)
                   ELSE jsonb_set(data, '{""" + key + """}', (COALESCE(data->'""" + key + """', '[]'::jsonb) || to_jsonb(:value ::jsonb)), true)
               END,
        updated = NOW()
    WHERE id = :id"""
    data = {"id": id, "value": value_json}
    execute(query, data)

def insertDocumentIngest(
    id,
    community_id,
    status,
    error_message,
    file_name,
    file_location,
    translation_document_id,
    upload_user_id,
):
    query = """
        INSERT INTO document_ingest (
            id,
            community_id,
            status,
            error_message,
            file_name,
            file_location,
            translation_document_id,
            upload_user_id
        ) VALUES (
            :id,
            :community_id,
            :status,
            :error_message,
            :file_name,
            :file_location,
            :translation_document_id,
            :upload_user_id
        )
    """
    data = {
        'id': id,
        'community_id': community_id,
        'status': status,
        'error_message': error_message,
        'file_name': file_name,
        'file_location': file_location,
        'translation_document_id': translation_document_id,
        'upload_user_id': upload_user_id
    }
    execute(query, data)


def getDocumentIngestById(id):
    query = "SELECT * FROM document_ingest WHERE id = :id"
    data = {'id': id}
    return select(query, data).first()


def getDocumentIngestByCommunityId(community_id, include_removed=False):
    query = "SELECT * FROM document_ingest WHERE community_id = :community_id AND status != 'REMOVED'"
    if include_removed:
        query = "SELECT * FROM document_ingest WHERE community_id = :community_id"
    data = {'community_id': str(community_id)}
    return select(query, data).all()


def updateDocumentIngestStatus(id, status, error_message=None):
    query = """
        UPDATE document_ingest
        SET status = :status,
            updated = NOW()"""
    data = {
        'id': id,
        'status': status
    }
    if error_message is not None:
        query += ", error_message = :error_message"
        data['error_message'] = error_message
    query += " WHERE id = :id"
    execute(query, data)


def updateDocumentIngestLocationAndStatus(id, file_location, status):
    """Update both file location and status in a single query"""
    query = """
        UPDATE document_ingest
        SET file_location = :file_location,
            status = :status,
            updated = NOW()
        WHERE id = :id
    """
    data = {
        'id': id,
        'file_location': file_location,
        'status': status
    }
    execute(query, data)


def deleteDocumentIngest(id):
    query = "DELETE FROM document_ingest WHERE id = :id"
    data = {'id': id}
    execute(query, data)


def getDocumentIngestByStatus(status):
    query = "SELECT * FROM document_ingest WHERE status = :status"
    data = {'status': status}
    return select(query, data).all()


def _build_document_search_conditions(community_ids, statuses=None, search=None):
    """Helper function to build search conditions for document queries

    Args:
        community_ids: Required. A list of community IDs.
        statuses: Optional. A list of statuses or None.
        search: Optional. A search string for file_name.
    """
    conditions = []
    data = {}

    data['community_ids'] = tuple(community_ids)
    conditions.append("community_id IN :community_ids")

    if statuses and len(statuses) > 0:
        data['statuses'] = tuple(statuses)
        conditions.append("status IN :statuses")

    if search:
        conditions.append("file_name ILIKE :search")
        data['search'] = f"%{search}%"

    return conditions, data


def countDocuments(community_ids, statuses=None, search=None):
    """Count total documents matching the search criteria without pagination

    Args:
        community_ids: Required. A list of community IDs.
        statuses: Optional. A list of statuses or None.
        search: Optional. A search string for file_name.
    """
    conditions, data = _build_document_search_conditions(community_ids, statuses, search)
    query = "SELECT COUNT(*) as count FROM document_ingest WHERE " + " AND ".join(conditions)
    result = select(query, data).first()
    return result['count'] if result else 0


def searchDocuments(community_ids, statuses=None, search=None, offset=0, limit=20, sort_field="file_upload_date", sort_order="DESC"):
    """Search documents with pagination, filtering, and sorting

    Args:
        community_ids: Required. A list of community IDs.
        statuses: Optional. A list of statuses or None.
        search: Optional. A search string for file_name.
        offset: Optional. Pagination offset. Default is 0.
        limit: Optional. Number of results to return. Default is 20.
        sort_field: Optional. Field to sort by. Default is "file_upload_date".
        sort_order: Optional. Sort order (ASC or DESC). Default is "DESC".
    """
    conditions, data = _build_document_search_conditions(community_ids, statuses, search)

    # Build the query
    query = "SELECT * FROM document_ingest WHERE " + " AND ".join(conditions)

    # Add sorting
    valid_sort_fields = ["file_name", "file_upload_date", "updated", "status"]
    valid_sort_orders = ["ASC", "DESC"]

    # Validate sort field and order
    if sort_field not in valid_sort_fields:
        sort_field = "file_upload_date"
    if sort_order not in valid_sort_orders:
        sort_order = "DESC"

    query += f" ORDER BY {sort_field} {sort_order}"

    # Add pagination
    query += " LIMIT :limit OFFSET :offset"
    data['limit'] = limit
    data['offset'] = offset

    return select(query, data).all()


def get_chats_by_company_id(company_id, days=None):
    query = """
        SELECT id, user_id, app, llm, tax, lang, created, updated, question, answer, score, comment
        FROM km.chat
        WHERE company_id = :company_id
    """
    data = {"company_id": company_id}
    if days is not None:
        query += " AND created >= NOW() - INTERVAL ':days days'"
        data["days"] = int(days)
    query += " ORDER BY created DESC"
    return select(query, data).all()


def get_chat_stats(company_id, days=None):
    """
    Returns stats for chats for a given company_id and optional days window.
    - request: total number of chats
    - thumbUp: count of chats with score == 1
    - thumbDown: count of chats with score == -1
    - comment: count of chats with non-null, non-empty comment
    """
    query = """
        SELECT
            COUNT(*) as request,
            COUNT(*) FILTER (WHERE score = 1) as thumb_up,
            COUNT(*) FILTER (WHERE score = -1) as thumb_down,
            COUNT(*) FILTER (WHERE comment IS NOT NULL AND comment <> '') as comment
        FROM km.chat
        WHERE company_id = :company_id
    """
    data = {"company_id": company_id}
    if days is not None:
        query += " AND created >= NOW() - INTERVAL ':days days'"
        data["days"] = int(days)
    result = select(query, data).first()
    return dict(result) if result else {"request": 0, "thumb_up": 0, "thumb_down": 0, "comment": 0}
