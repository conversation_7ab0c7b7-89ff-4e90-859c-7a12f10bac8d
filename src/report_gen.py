import html
import re
from typing import Optional
import logging

from pydantic import BaseModel
from translate import translate
import report_prompts
from gen import GenParams, gen
from markdown import Markdown
from io import StringIO
from time import sleep

logger = logging.getLogger(__name__)

def unmark_element(element, stream=None):
    if stream is None:
        stream = StringIO()
    if element.text:
        stream.write(element.text)
    for sub in element:
        unmark_element(sub, stream)
    if element.tail:
        stream.write(element.tail)
    return stream.getvalue()


# patching Markdown
Markdown.output_formats["plain"] = unmark_element
__md = Markdown(output_format="plain")
__md.stripTopLevelTags = False


def unmark(text):
    return __md.convert(text)

class ReportParams(BaseModel):
    template: str
    prompt: str
    model: str
    taxonomy: str
    lang: str = "en"
    user: Optional[str] = None


def parse_template(template: str):
    return re.findall(r'{([^}]+)}', template)


def create_gen_param(params: ReportParams, question: str) -> GenParams:
    gp = GenParams(
        question=question,
        model=params.model,
        taxonomy=params.taxonomy,
        lang=params.lang,
        user=params.user,
        prompt=params.prompt,
        k_docs=8,
        temperature=0,
        max_tokens=1024,
        repetition_penalty=1,
        translate_output=False)
    return gp


def gen_answer(gen_param: GenParams):
    try:
        return gen(gen_param, 'report_question')
    except Exception as e:
        logger.error(f"gen error, recovering in 15secs {e}")
        sleep(15)
        return gen(gen_param, 'report_question')


def process_sources(answers):
    sources = {}
    contents = {}
    i = 0
    for question, response in answers.items():
        ctx = response['context']
        response['sources'] = []
        for doc in ctx:
            content = doc.page_content
            metadata = doc.metadata
            if content not in contents:
                i += 1
                contents[content] = i

            metadata['src_order'] = contents[content]
            response['sources'].append(metadata['src_order'])
            sources[metadata['src_order']] = {'content': content, "src": metadata['source']}

    return sources


def sources_md(sources):
    md = ''
    for key,val in sources.items():
        txt = val['content']
        src = val['src']
        txt = re.sub(r"<metadata>.*?</metadata>", "", txt, flags=re.DOTALL).strip()
        txt = html.escape(txt)
        md += f"<details><summary>[{key}] {src}</summary>\n\n  {txt}</details>\n\n"
    return md


def replace_empty_lines(text):
    lines = text.splitlines()
    modified_lines = [line if line.strip() else "&nbsp;  " for line in lines]
    return "\n".join(modified_lines)


def process_answers(template, answers):
    template = replace_empty_lines(template)
    sources = process_sources(answers)
    for question, response in answers.items():

        reply = unmark(response['answer'])
        value = f"<details open><summary>{question}</summary>\n&nbsp;  \n\n  {reply}  \n\nSources: {response['sources']}</details>\n&nbsp;  \n  "
        key = "{" + question + "}"
        template = template.replace(key, value)

    return template, sources_md(sources)


def gen_report(params: ReportParams):
    questions = parse_template(params.template)
    answers = {}
    for question in questions:
        gen_param = create_gen_param(params, question)
        answers[question] = gen_answer(gen_param)

    out, sources = process_answers(params.template, answers)
    tr_out = tr(out,params.lang) if params.lang != 'en' else out
    # todo save full response
    logger.info(f"out: {out}")
    return {'report': tr_out, 'sources': sources}


def tr(out, lang):
    try:
        return translate(out, lang)
    except:
        return out


if __name__ == "__main__":
    p = ReportParams(
        model='llama3',
        lang='en',
        user='admin',
        template=report_prompts.TEMPLATES['demo']['template'],
        prompt=report_prompts.PROMPTS['rag']['prompt'],
        taxonomy='rob',
    )
    out = gen_report(p)
    print(out)
