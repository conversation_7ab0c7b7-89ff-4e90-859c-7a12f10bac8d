demo_create_deploy:
  stage: deploy
  rules:
    - if: $CI_COMMIT_TAG
      when: manual
  script:
    - docker login -u "$CI_REGISTRY_USER" -p "$CI_REGISTRY_PASSWORD" $CI_REGISTRY
    - >-
      docker -H demo.exfluency.com:2387 service create
      --name exfluency_exfluency-knowledge-mining-api
      --network name=exfluency_default,alias=exfluency-knowledge-mining-api
      --publish 4116:4116
      --with-registry-auth
      --env OPENAI_API_KEY=********************************************************************************************************************************************************************
      --env TOGETHER_API_KEY=tgp_v1_OZum4UyQ69oW-7tDKAxGEkMeHlzgumqqb-n82oeu-40
      --env PINECONE_API_KEY=pcsk_7r7DM_4ytTgN8Bstx3gPQNNKJMnqMdSn1gmR9D9yvEJRXteKHa312NXtAGEeLPJKkcbhE
      --env DEEPL_API_KEY=72aa993d-cb73-d9b1-c05f-04de5f586797
      --env GROQ_API_KEY=********************************************************
      --env DB_USER=exfluency
      --env DB_PASS=sdkh845nDF93SsdDsxZ8wHZNJ55GJzFBJ
      --env DB_HOST=postgres
      --env DB_PORT=5432
      --env DB_NAME=exfluency
      --env MY_PORT=4116
      --env INDEX=demo
      $CI_REGISTRY_IMAGE:$CI_COMMIT_TAG
    - docker -H demo.exfluency.com:2387 system prune -f
