main_create_deploy:
  stage: deploy
  rules:
    - if: $CI_COMMIT_TAG
      when: manual
  script:
    - docker login -u "$CI_REGISTRY_USER" -p "$CI_REGISTRY_PASSWORD" $CI_REGISTRY
    - >-
      docker -H search-and-create.prod.ua.exfluency-cloud.com:2387 service create
      --name exfluency_knowledge-mining-api
      --publish 4116:4116
      --with-registry-auth
      --env OPENAI_API_KEY=********************************************************************************************************************************************************************
      --env TOGETHER_API_KEY=tgp_v1_OZum4UyQ69oW-7tDKAxGEkMeHlzgumqqb-n82oeu-40
      --env PINECONE_API_KEY=pcsk_5PjwJH_6ZmAs3e6mfJsnchbJ2cqBVgcB1SHVDBNrNSxk9GKS3WhJCZ9jbyBs3cYZqeQ33o
      --env DEEPL_API_KEY=72aa993d-cb73-d9b1-c05f-04de5f586797
      --env GROQ_API_KEY=********************************************************
      --env DB_USER=exfluency
      --env DB_PASS=jgu5Nt43eTfg3F5Gthlp
      --env DB_HOST=db.prod.ua.exfluency-cloud.com
      --env DB_PORT=5432
      --env DB_NAME=exfluency
      --env MY_PORT=4116
      --env INDEX=main
      --network name=search-and-create_default,alias=knowledge-mining-api
      $CI_REGISTRY_IMAGE:$CI_COMMIT_TAG
    - docker -H search-and-create.prod.ua.exfluency-cloud.com:2387 system prune -f
