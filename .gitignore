# See https://help.github.com/articles/ignoring-files/ for more about ignoring files.

*~
/venv
**/semanticscholar_pdfs
__pycache__
/data
/proc
/.idea
# dependencies
/node_modules
/.pnp
.pnp.js

# testing
/coverage

# next.js
/.next/
/out/

# production
/build

# misc
.DS_Store
*.pem

# debug
npm-debug.log*
yarn-debug.log*
yarn-error.log*
.pnpm-debug.log*


# vercel
.vercel

# typescript
*.tsbuildinfo
next-env.d.ts

#Notion_db
/Notion_DB

.langchain.db

# Added by Task Master AI
# Logs
logs
*.log
dev-debug.log
# Dependency directories
node_modules/
# Environment variables
.env
# Editor directories and files
.idea
.vscode
*.suo
*.ntvs*
*.njsproj
*.sln
*.sw?
# OS specific
# Task files
tasks.json
tasks/ 

.cursor/
.env.example
.roo/
.roomodes
.taskmaster/
.windsurfrules
