import time
import uuid
import os
import sys
import pytest
import dotenv

dotenv.load_dotenv()

current_dir = os.path.dirname(__file__)
server_path = os.path.abspath(os.path.join(current_dir, '..', 'src'))
sys.path.insert(0, server_path)

from ingest.process import DocumentProcessor
import db

@pytest.mark.asyncio
async def test_document_processing():
    # Create an instance of DocumentProcessor
    document_processor = DocumentProcessor()

    # Define the path to the test PDF
    test_pdf_path = os.path.join(current_dir, "data", "test.pdf")

    # Step 1: Upload the document
    with open(test_pdf_path, 'rb') as file:
        file_content = file.read()
        community_id = uuid.uuid4()
        user_id = uuid.uuid4()
        document_id = document_processor.upload_file(file_content, "test.pdf", community_id, user_id)

    assert document_id is not None
    try:
        # Step 2: Process the document
        document_processor.process_document(document_id)

        # Step 3: Query the document via text using Pinecone
        query_text = "grocery list"
        vector = document_processor.vecstore.pinecone_embeder.embed_query(query_text)

        # Retry logic for querying Pinecone,
        # as written in Pinecone doc: It can take up to 35 seconds before upserted records are available to query.
        max_retries = 3
        for attempt in range(max_retries):
            query_result = document_processor.vecstore.pinecone_wrapper.query(vector, top_k=5, includeMetadata=True, namespace=str(community_id))

            if query_result is not None and len(query_result.get('matches', [])) > 0:
                break  # Exit the loop if the query is successful
            else:
                if attempt < max_retries - 1:  # Don't sleep after the last attempt
                    print(f"Query attempt {attempt + 1} failed. Retrying in 10 seconds...")
                    time.sleep(10)
                else:
                    raise AssertionError("Query failed after 3 attempts. No matches found.")


        assert query_result is not None
        assert len(query_result.get('matches', [])) > 0  # Ensure there are matches

    finally:
        # Step 4: Remove the document
        document_processor.remove_document(document_id, full=True)

        # Verify that the document has been removed
        removed_doc = db.getDocumentIngestById(document_id)
        assert removed_doc is None

        # Cleanup Pinecone index
        document_processor.vecstore.pinecone_wrapper.delete_namespace(str(community_id))