from fastapi import FastAP<PERSON>
from fastapi.testclient import TestClient
from uuid import uuid4
import os
import sys
import dotenv
from src.ingest.process import DocumentStatus

dotenv.load_dotenv()

current_dir = os.path.dirname(__file__)

server_path = os.path.abspath(os.path.join(current_dir, '..', 'src'))
sys.path.insert(0, server_path)
# Create a test FastAPI app
from ingest.rest_ingest import router
app = FastAPI()
app.include_router(router)
client = TestClient(app)

def test_document_search_endpoint():
    # Generate a test community ID
    community_id = str(uuid4())

    # Test the endpoint with various parameters

    # Test with required communityId parameter
    response = client.get(f"/api/km/ingest/documents?communityId={community_id}")
    assert response.status_code == 200
    assert isinstance(response.json(), list)
    assert "x-total-count" in response.headers
    assert response.headers["x-total-count"].isdigit()

    # Test with multiple parameters
    response = client.get(
        f"/api/km/ingest/documents?communityId={community_id}&status={DocumentStatus.INGESTED.value}&_offset=0&_limit=10&_sort=file_upload_date&_order=DESC"
    )
    assert response.status_code == 200
    assert isinstance(response.json(), list)

    # Test with search parameter
    response = client.get(
        f"/api/km/ingest/documents?communityId={community_id}&search=test&_sort=file_name&_order=ASC"
    )
    assert response.status_code == 200
    assert isinstance(response.json(), list)

    # Test with multiple community IDs
    community_id2 = str(uuid4())
    response = client.get(
        f"/api/km/ingest/documents?communityId={community_id}&communityId={community_id2}"
    )
    assert response.status_code == 200
    assert isinstance(response.json(), list)

    # Test with multiple statuses
    response = client.get(
        f"/api/km/ingest/documents?communityId={community_id}&status={DocumentStatus.INGESTED.value}&status={DocumentStatus.UPLOADED.value}"
    )
    assert response.status_code == 200
    assert isinstance(response.json(), list)

    # Test with invalid sort field
    response = client.get(
        f"/api/km/ingest/documents?communityId={community_id}&_sort=invalid_field"
    )
    assert response.status_code == 422  # Validation error

    # Test with invalid sort order
    response = client.get(
        f"/api/km/ingest/documents?communityId={community_id}&_order=INVALID"
    )
    assert response.status_code == 422  # Validation error

    # Test with a non-existent community ID (should return empty list with count=0)
    non_existent_id = str(uuid4())  # Generate a random UUID that shouldn't exist
    response = client.get(f"/api/km/ingest/documents?communityId={non_existent_id}")
    assert response.status_code == 200
    assert response.headers["x-total-count"] == "0"
    assert response.json() == []  # Should be an empty list

    # Test that total count matches the number of records when limit is large
    response = client.get(
        f"/api/km/ingest/documents?communityId={community_id}&_limit=1000"
    )
    assert response.status_code == 200
    total_count = int(response.headers["x-total-count"])
    result_count = len(response.json())
    # The total count should be equal to or greater than the actual results
    # (they should be equal if there are fewer than 1000 records)
    assert total_count >= result_count

if __name__ == "__main__":
    test_document_search_endpoint()
