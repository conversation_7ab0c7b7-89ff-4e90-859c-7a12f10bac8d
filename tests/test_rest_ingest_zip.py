import os
import time
import uuid
from fastapi import FastAPI
from fastapi.testclient import Test<PERSON>lient
import pytest
import sys

current_dir = os.path.dirname(__file__)
server_path = os.path.abspath(os.path.join(current_dir, '..', 'src'))
sys.path.insert(0, server_path)
# Create a test FastAPI app
from ingest.rest_ingest import router

# Create a test FastAPI app
app = FastAPI()
app.include_router(router)


def test_document_upload_process_fetch_delete_flow():
    """
    End-to-end test that goes through the entire document flow:
    1. Upload a ZIP file containing multiple documents
    2. Poll for processing status until INGESTED or timeout for each document
    3. Fetch each document by ID
    4. Fetch all documents by community ID
    5. Mark documents as REMOVED
    6. Verify documents are marked as REMOVED
    """

    # Set up the test client
    client = TestClient(app)

    # Create test IDs
    community_id = str(uuid.uuid4())
    user_id = str(uuid.uuid4())

    # Path to test ZIP file
    test_zip_path = os.path.join(current_dir, "data", "test3.zip")

    # Check if test file exists
    assert os.path.exists(test_zip_path), f"Test ZIP file not found at {test_zip_path}"

    # 1. Upload ZIP file containing multiple documents
    with open(test_zip_path, "rb") as file:
        response = client.post(
            f"/api/km/ingest/document?community_id={community_id}&user_id={user_id}",
            files={"file": ("test3.zip", file, "application/zip")}
        )

    # Verify successful upload
    assert response.status_code == 200
    assert isinstance(response.json(), list)
    assert len(response.json()) == 2, "Expected 2 documents (test.pdf and test2.docx) in ZIP file"

    # Get the document IDs
    document_ids = response.json()

    # 2. Poll document status until they're all INGESTED
    max_attempts = 5
    polling_interval = 5  # seconds

    for document_id in document_ids:
        print(f"Checking document {document_id}")

        for attempt in range(max_attempts):
            # Get document status
            response = client.get(f"/api/km/ingest/document/{document_id}")

            # Check if request was successful
            assert response.status_code == 200, f"Failed to get document on attempt {attempt + 1}"

            # Check document status
            document = response.json()
            print(f"Attempt {attempt + 1}: Document status = {document.get('status', 'unknown')}")

            if document.get('status') == "INGESTED":
                print(f"Document {document_id} successfully ingested!")
                break

            # Not done yet, wait before next attempt
            if attempt < max_attempts - 1:
                time.sleep(polling_interval)
        else:
            # This executes if the for loop completes without a break
            pytest.fail(f"Document {document_id} did not reach INGESTED status after {max_attempts} attempts")

    # 3. Fetch each document by ID
    all_documents = []
    for document_id in document_ids:
        response = client.get(f"/api/km/ingest/document/{document_id}")

        assert response.status_code == 200
        document = response.json()
        assert isinstance(document, dict)
        assert "file_name" in document
        assert document["file_name"] in ["test.pdf", "test2.docx"], f"Unexpected filename: {document['file_name']}"
        assert str(document["community_id"]) == community_id

        all_documents.append(document)

    # Verify we got both files
    filenames = [doc["file_name"] for doc in all_documents]
    assert "test.pdf" in filenames, "test.pdf not found in processed documents"
    assert "test2.docx" in filenames, "test2.docx not found in processed documents"

    # 4. Fetch documents by community ID
    response = client.get(f"/api/km/ingest/documents/{community_id}")

    # Verify community documents retrieval
    assert response.status_code == 200
    assert isinstance(response.json(), list)
    assert len(response.json()) >= 2  # There should be at least our 2 uploaded documents

    # Find our documents in the list
    community_docs = response.json()
    found_docs = 0
    for doc_id in document_ids:
        for community_doc in community_docs:
            if community_doc["id"] == doc_id:
                found_docs += 1
                break

    assert found_docs == 2, f"Not all uploaded documents found in community documents. Found {found_docs}/2"

    # 5. Mark documents as REMOVED one by one
    for document_id in document_ids:
        response = client.delete(f"/api/km/ingest/document/{document_id}")

        # Verify successful "deletion" (status change)
        assert response.status_code == 200


    # 6. Verify each document is marked as REMOVED
    for document_id in document_ids:
        response = client.get(f"/api/km/ingest/document/{document_id}")

        # Document should still exist but with REMOVED status
        assert response.status_code == 200
        document = response.json()
        assert document["status"] == "REMOVED"

    # 7. Verify they're not included in active documents for the community
    response = client.get(f"/api/km/ingest/documents/{community_id}")

    # If there are no active documents left, we might get a 404
    if response.status_code == 404:
        # This is fine - means no active documents remain
        pass
    elif response.status_code == 200:
        # If we get documents back, make sure none are our removed ones
        community_docs = response.json()
        for doc in community_docs:
            assert doc["status"] == "REMOVED", f"Document {doc['id']} still appears as active"

    print("End-to-end document flow test with ZIP file completed successfully")


if __name__ == "__main__":
    # This allows running the test directly
    test_document_upload_process_fetch_delete_flow()