import os

import time
import uuid
from fastapi import FastAPI
from fastapi.testclient import Test<PERSON>lient
import pytest

import sys
import dotenv



dotenv.load_dotenv()

current_dir = os.path.dirname(__file__)

server_path = os.path.abspath(os.path.join(current_dir, '..', 'src'))
sys.path.insert(0, server_path)
# Create a test FastAPI app
from ingest.rest_ingest import router


app = FastAPI()
app.include_router(router)

TEST_PDF = "test_ocr.pdf"

def test_document_upload_process_fetch_delete_flow():
    """
    End-to-end test that goes through the entire document flow:
    1. Upload a document
    2. Wait for processing
    3. Fetch by document ID
    4. Fetch by community ID
    5. Delete document
    6. Verify deletion
    """

    # Set up the test client
    client = TestClient(app)

    # Create test IDs
    community_id = str(uuid.uuid4())
    user_id = str(uuid.uuid4())

    # Path to test PDF file
    test_file_path = os.path.join(current_dir, "data", TEST_PDF)

    # Check if test file exists
    assert os.path.exists(test_file_path), f"Test file not found at {test_file_path}"

    # 1. Upload document
    with open(test_file_path, "rb") as file:
        response = client.post(
            f"/api/km/ingest/document?community_id={community_id}&user_id={user_id}",
            files={"file": (TEST_PDF, file, "application/pdf")}
        )

    # Verify successful upload
    assert response.status_code == 200
    assert isinstance(response.json(), list)
    assert len(response.json()) == 1

    # Get the document ID
    document_id = response.json()[0]

    # 2. Poll document status until it's INGESTED
    max_attempts = 5
    polling_interval = 5  # seconds

    for attempt in range(max_attempts):
        # Get document status
        response = client.get(f"/api/km/ingest/document/{document_id}")

        # Check if request was successful
        assert response.status_code == 200, f"Failed to get document on attempt {attempt + 1}"

        # Check document status
        document = response.json()
        print(f"Attempt {attempt + 1}: Document status = {document.get('status', 'unknown')}")

        if document.get('status') == "INGESTED":
            print("Document successfully ingested!")
            break

        # Not done yet, wait before next attempt
        if attempt < max_attempts - 1:
            time.sleep(polling_interval)
    else:
        # This executes if the for loop completes without a break
        pytest.fail(f"Document did not reach INGESTED status after {max_attempts} attempts")

    # 3. Fetch document by ID
    response = client.get(f"/api/km/ingest/document/{document_id}")

    # Verify document retrieval
    assert response.status_code == 200
    assert isinstance(response.json(), dict)
    assert "file_name" in response.json()
    assert response.json()["file_name"] == TEST_PDF
    assert str(response.json()["community_id"]) == community_id

    # 4. Fetch documents by community ID
    response = client.get(f"/api/km/ingest/documents/{community_id}")

    # Verify community documents retrieval
    assert response.status_code == 200
    assert isinstance(response.json(), list)
    assert len(response.json()) >= 1  # There should be at least our uploaded document

    # Find our document in the list
    found = False
    for doc in response.json():
        if doc["id"] == document_id:
            found = True
            break

    assert found, "Uploaded document not found in community documents"

    response = client.delete(f"/api/km/ingest/document/{document_id}")

    # Verify successful "deletion" (status change)
    assert response.status_code == 200


    # 6. Verify the document is marked as REMOVED
    response = client.get(f"/api/km/ingest/document/{document_id}")

    # Document should still exist but with REMOVED status
    assert response.status_code == 200
    document = response.json()
    assert document["status"] == "REMOVED"

    # 7. Verify it's not included in active documents for the community
    response = client.get(f"/api/km/ingest/documents/{community_id}")

    if response.status_code == 200:
        # Document should not be in the active documents list
        for doc in response.json():
            if doc["id"] == document_id:
                assert doc["status"] == "REMOVED", "Document not marked as REMOVED in community list"

    print("End-to-end document flow test completed successfully")


if __name__ == "__main__":
    # This allows running the test directly
    test_document_upload_process_fetch_delete_flow()
