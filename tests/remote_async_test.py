import os
import asyncio
import httpx
import dotenv

# Load environment variables
dotenv.load_dotenv()

current_dir = os.path.dirname(__file__)
HOST = "http://localhost:4116"  # Default, can be overridden by env
HOST = os.getenv("HOST", HOST)


async def fetch_hello(client, idx, path="/api/km/hello"):
    try:
        response = await client.get(f"{HOST}{path}?id={idx}", timeout=40)
        print(f"Request {idx}: Status {response.status_code}")
        return response.status_code
    except Exception as e:
        print(f"Request {idx}: Exception {str(e)}", e)
        return e

async def main():
    async with httpx.AsyncClient() as client:
        tasks = [fetch_hello(client, i, path="/api/km/models") for i in range(100)]
        results = await asyncio.gather(*tasks)
    print("All statuses:", results)

if __name__ == "__main__":
    asyncio.run(main()) 