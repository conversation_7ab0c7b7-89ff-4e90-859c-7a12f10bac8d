import os


import uuid
from fastapi import FastAP<PERSON>
from fastapi.testclient import TestClient
import pytest

import sys
import dotenv

dotenv.load_dotenv()

current_dir = os.path.dirname(__file__)

server_path = os.path.abspath(os.path.join(current_dir, '..', 'src'))
sys.path.insert(0, server_path)

from ingest.rest_ingest import router
app = FastAPI()
app.include_router(router)
client = TestClient(app)
from ingest.process import DocumentProcessor


@pytest.mark.asyncio
async def test_nonexistent_document_remove():

    document_processor = DocumentProcessor()

    # Define the path to the test PDF
    test_pdf_path = os.path.join(current_dir, "data", "test.pdf")

    # Step 1: Upload the document
    with open(test_pdf_path, 'rb') as file:
        file_content = file.read()
        community_id = uuid.uuid4()
        user_id = uuid.uuid4()
        document_id = document_processor.upload_file(file_content, "test.pdf", community_id, user_id)

    # at this point document not exists in vector store
    response = client.delete(f"/api/km/ingest/document/{document_id}")
    assert 200 == response.status_code