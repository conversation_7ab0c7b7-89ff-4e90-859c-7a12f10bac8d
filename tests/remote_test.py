import os
import time
import uuid

import dotenv
import requests

# TEST_FILE = "test_big_ocr_22-30.pdf"
TEST_FILE = "test.pdf"

dotenv.load_dotenv()

current_dir = os.path.dirname(__file__)

HOST = "http://localhost:4116"


def timed_request(method, url, description, **kwargs):
    """Helper function to time HTTP requests and print timing information"""
    start_time = time.time()
    response = getattr(requests, method)(url, **kwargs)
    duration = time.time() - start_time
    print(f"⏱️  {description}: {duration:.3f} seconds")
    return response

if __name__ == "__main__":
    community_id = str(uuid.uuid4())
    user_id = str(uuid.uuid4())
    company_id = str(uuid.uuid4())

    # Path to test PDF file
    test_file_path = os.path.join(current_dir, "data", TEST_FILE)

    # Check if test file exists
    assert os.path.exists(test_file_path), f"Test file not found at {test_file_path}"

    # 1. Upload document
    with open(test_file_path, "rb") as file:
        response = timed_request(
            "post",
            f"{HOST}/api/km/ingest/document?community_id={community_id}&user_id={user_id}",
            "Document upload",
            files={"file": (TEST_FILE, file, "application/pdf")}
        )

    assert response.status_code == 200
    assert isinstance(response.json(), list)
    assert len(response.json()) == 1

    # Get the document ID
    document_id = response.json()[0]

    max_attempts = 50
    polling_interval = 5  # seconds

    for attempt in range(max_attempts):
        # Get document status
        response = timed_request(
            "get",
            f"{HOST}/api/km/ingest/document/{document_id}",
            f"Document status check (attempt {attempt + 1})"
        )

        # Check if request was successful
        assert response.status_code == 200, f"Failed to get document on attempt {attempt + 1}"

        # Check document status
        document = response.json()
        print(f"Attempt {attempt + 1}: Document status = {document.get('status', 'unknown')}")

        if document.get('status') == "INGESTED":
            print("Document successfully ingested!")
            break
        elif document.get('status') == "ERROR":
            raise AssertionError(f"Document ingestion failed with status ERROR: {document}")

        # Not done yet, wait before next attempt
        if attempt < max_attempts - 1:
            time.sleep(polling_interval)
    else:
        # This executes if the for loop completes without a break
        print(f"Document did not reach INGESTED status after {max_attempts} attempts")
        raise AssertionError(f"Document did not reach INGESTED status after {max_attempts} attempts")


    # 3. Fetch document by ID
    response = timed_request(
        "get",
        f"{HOST}/api/km/ingest/document/{document_id}",
        "Document fetch by ID"
    )

    # Verify document retrieval
    assert response.status_code == 200
    assert isinstance(response.json(), dict)
    assert "file_name" in response.json()
    assert response.json()["file_name"] == TEST_FILE
    assert str(response.json()["community_id"]) == community_id



    # 4. Chat endpoint test
    chat_payload = {
        "question": "What is the content of the document?",
        "history": []
    }
    chat_params = {
        "model": "kimi2",
        "lang": "en",
        "user": user_id,
        "tax": community_id,
        "company_id": company_id
    }

    response = timed_request(
        "post",
        f"{HOST}/api/km/chat",
        "Chat endpoint",
        params=chat_params,
        json=chat_payload
    )
    assert response.status_code == 200, f"Chat endpoint with company_id failed: {response.text}"
    print("/api/km/chat response with company_id:", response.json())

    # Test /api/km/chats CSV export
    response = timed_request(
        "get",
        f"{HOST}/api/km/chats",
        "Chats CSV export",
        params={"company_id": company_id}
    )
    assert response.status_code == 200, f"/api/km/chats failed: {response.text}"
    content_type = response.headers.get("content-type", "")
    assert "text/csv" in content_type, f"Expected CSV content-type, got {content_type}"
    csv_text = response.text
    print("/api/km/chats CSV output:\n", csv_text)
    assert "id,user_id,app,llm,tax,lang,created,updated,question,answer,score,comment" in csv_text, "CSV header missing"
    assert chat_payload["question"] in csv_text, "Chat question not found in CSV output"

    # Test /api/km/chat/stats endpoint
    response = timed_request(
        "get",
        f"{HOST}/api/km/chat/stats",
        "Chat stats endpoint",
        params={"company_id": company_id}
    )
    assert response.status_code == 200, f"/api/km/chat/stats failed: {response.text}"
    stats = response.json()
    print("/api/km/chat/stats output:", stats)
    assert all(key in stats for key in ["request", "thumb_up", "thumb_down", "comment"]), "Missing keys in stats response"

    response = timed_request(
        "delete",
        f"{HOST}/api/km/ingest/document/{document_id}",
        "Document delete"
    )
    assert response.status_code == 200, f"Delete endpoint failed: {response.text}"

    # 5. Gen endpoint test
    gen_payload = {
        "question": "Summarize the document.",
        "history": [],
        "model": "mixtral2",
        "taxonomy": community_id,
        "lang": "en",
        "user": user_id,
        "prompt": "Use only 5 words"
    }

    response = timed_request(
        "post",
        f"{HOST}/api/km/gen",
        "Gen endpoint",
        params={"user": user_id},
        json=gen_payload
    )

    assert response.status_code == 200, f"Gen endpoint without company_id failed: {response.text} {response.status_code}"
    print("/api/km/gen response without company_id:", response.json())

    print("End-to-end document flow test completed successfully")