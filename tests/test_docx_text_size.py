"""
DOCX Text Size Checker

This module provides efficient methods to check the text size (character count) 
of docx files without loading the entire content into memory.

Usage Examples:
    # Quick check with default test file
    python test_docx_text_size.py
    
    # Check specific file by changing test_filename variable
    test_filename = "your_file.docx"
    
    # Use in your code:
    from test_docx_text_size import get_text_size_quick
    size = get_text_size_quick("document.docx", "/path/to/files")
    
    # Or comprehensive comparison:
    from test_docx_text_size import check_docx_text_size
    results = check_docx_text_size("document.docx", "/path/to/files")
"""

import os
import sys
import time
import zipfile
import xml.etree.ElementTree as ET
from io import BytesIO
import pytest
import docx2txt
import html

# Add src directory to path
current_dir = os.path.dirname(__file__)
server_path = os.path.abspath(os.path.join(current_dir, '..', 'src'))
sys.path.insert(0, server_path)

class DocxTextSizeChecker:
    """
    Collection of methods to efficiently check text size in docx files 
    without loading the entire content into memory
    """
    
    @staticmethod
    def get_text_length_docx2txt(file_path: str) -> int:
        """
        Use docx2txt to extract text and return only the length
        Memory efficient as we don't store the full text
        """
        try:
            text = docx2txt.process(file_path)
            return len(text) if text else 0
        except Exception as e:
            print(f"Error with docx2txt: {e}")
            return 0
    
    @staticmethod
    def get_text_length_zip_parsing(file_path: str) -> int:
        """
        Directly parse the docx as ZIP file and extract text from XML
        Memory efficient approach using streaming XML tag removal
        """
        try:
            total_length = 0
            with zipfile.ZipFile(file_path, 'r') as docx_zip:
                # Get the main document content
                try:
                    with docx_zip.open('word/document.xml') as doc_xml:
                        # Process the XML content in chunks to avoid loading entire document
                        chunk_size = 8192  # 8KB chunks
                        buffer = ""
                        
                        while True:
                            chunk = doc_xml.read(chunk_size)
                            if not chunk:
                                break
                                
                            # Convert bytes to string and add to buffer
                            buffer += chunk.decode('utf-8', errors='ignore')
                            
                            # Process all complete w:t elements in the buffer
                            while '<w:t' in buffer and '</w:t>' in buffer:
                                # Find the start of w:t tag
                                start_idx = buffer.find('<w:t')
                                if start_idx == -1:
                                    break
                                    
                                # Find the end of the opening tag
                                tag_end_idx = buffer.find('>', start_idx)
                                if tag_end_idx == -1:
                                    break
                                    
                                # Find the closing tag
                                close_tag_idx = buffer.find('</w:t>', tag_end_idx)
                                if close_tag_idx == -1:
                                    break
                                    
                                # Extract text content between tags
                                text_content = buffer[tag_end_idx + 1:close_tag_idx]
                                # Handle XML entities (like &amp;, &lt;, etc.)
                                text_content = html.unescape(text_content)
                                total_length += len(text_content)
                                
                                # Remove the processed part from buffer
                                buffer = buffer[close_tag_idx + 6:]  # 6 is length of '</w:t>'
                            
                            # Keep only the last part that might contain incomplete tags
                            if '<w:t' in buffer:
                                last_tag_start = buffer.rfind('<w:t')
                                buffer = buffer[last_tag_start:]
                            else:
                                buffer = ""
                        
                        # Process any remaining complete w:t elements in the final buffer
                        while '<w:t' in buffer and '</w:t>' in buffer:
                            start_idx = buffer.find('<w:t')
                            if start_idx == -1:
                                break
                                
                            tag_end_idx = buffer.find('>', start_idx)
                            if tag_end_idx == -1:
                                break
                                
                            close_tag_idx = buffer.find('</w:t>', tag_end_idx)
                            if close_tag_idx == -1:
                                break
                                
                            text_content = buffer[tag_end_idx + 1:close_tag_idx]
                            text_content = html.unescape(text_content)
                            total_length += len(text_content)
                            
                            buffer = buffer[close_tag_idx + 6:]
                                
                except KeyError:
                    print("document.xml not found in docx file")
                    return 0
                    
            return total_length
        except Exception as e:
            print(f"Error with ZIP parsing: {e}")
            return 0
    
    @staticmethod
    def get_text_length_from_bytes(file_content: bytes) -> int:
        """
        Extract text length directly from file bytes without writing to disk
        Memory efficient implementation using streaming XML tag removal
        """
        try:
            total_length = 0
            
            with zipfile.ZipFile(BytesIO(file_content), 'r') as docx_zip:
                try:
                    with docx_zip.open('word/document.xml') as doc_xml:
                        # Process the XML content in chunks to avoid loading entire document
                        chunk_size = 8192  # 8KB chunks
                        buffer = ""
                        
                        while True:
                            chunk = doc_xml.read(chunk_size)
                            if not chunk:
                                break
                                
                            # Convert bytes to string and add to buffer
                            buffer += chunk.decode('utf-8', errors='ignore')
                            
                            # Process all complete w:t elements in the buffer
                            while '<w:t' in buffer and '</w:t>' in buffer:
                                # Find the start of w:t tag
                                start_idx = buffer.find('<w:t')
                                if start_idx == -1:
                                    break
                                    
                                # Find the end of the opening tag
                                tag_end_idx = buffer.find('>', start_idx)
                                if tag_end_idx == -1:
                                    break
                                    
                                # Find the closing tag
                                close_tag_idx = buffer.find('</w:t>', tag_end_idx)
                                if close_tag_idx == -1:
                                    break
                                    
                                # Extract text content between tags
                                text_content = buffer[tag_end_idx + 1:close_tag_idx]
                                # Handle XML entities (like &amp;, &lt;, etc.)
                                text_content = html.unescape(text_content)
                                total_length += len(text_content)
                                
                                # Remove the processed part from buffer
                                buffer = buffer[close_tag_idx + 6:]  # 6 is length of '</w:t>'
                            
                            # Keep only the last part that might contain incomplete tags
                            if '<w:t' in buffer:
                                last_tag_start = buffer.rfind('<w:t')
                                buffer = buffer[last_tag_start:]
                            else:
                                buffer = ""
                        
                        # Process any remaining complete w:t elements in the final buffer
                        while '<w:t' in buffer and '</w:t>' in buffer:
                            start_idx = buffer.find('<w:t')
                            if start_idx == -1:
                                break
                                
                            tag_end_idx = buffer.find('>', start_idx)
                            if tag_end_idx == -1:
                                break
                                
                            close_tag_idx = buffer.find('</w:t>', tag_end_idx)
                            if close_tag_idx == -1:
                                break
                                
                            text_content = buffer[tag_end_idx + 1:close_tag_idx]
                            text_content = html.unescape(text_content)
                            total_length += len(text_content)
                            
                            buffer = buffer[close_tag_idx + 6:]
                    
                except KeyError:
                    print("document.xml not found in docx file")
                    return 0
                    
            return total_length
        except Exception as e:
            print(f"Error with bytes parsing: {e}")
            return 0

def check_docx_text_size(filename: str, test_data_dir: str = None) -> dict:
    """
    Check text size of a docx file using different methods
    
    Args:
        filename: Name of the docx file to test
        test_data_dir: Directory containing the test file (defaults to tests/data)
    
    Returns:
        Dictionary with results from different methods
    """
    if test_data_dir is None:
        test_data_dir = os.path.join(current_dir, "data")
    
    file_path = os.path.join(test_data_dir, filename)
    
    if not os.path.exists(file_path):
        raise FileNotFoundError(f"File not found: {file_path}")
    
    checker = DocxTextSizeChecker()
    
    print(f"\n{'='*50}")
    print(f"Testing file: {filename}")
    print(f"File size: {os.path.getsize(file_path) / 1024 / 1024:.2f} MB")
    print(f"{'='*50}")
    
    # Test each method and measure time
    methods = [
        ("docx2txt", checker.get_text_length_docx2txt),
        ("zip_parsing", checker.get_text_length_zip_parsing),
    ]
    
    results = {}
    
    for method_name, method_func in methods:
        start_time = time.time()
        text_length = method_func(file_path)
        end_time = time.time()
        
        results[method_name] = {
            'length': text_length,
            'time': end_time - start_time
        }
        
        print(f"{method_name:15}: {text_length:,} chars in {end_time - start_time:.3f}s")
    
    # Test bytes method
    with open(file_path, 'rb') as f:
        file_content = f.read()
        
    start_time = time.time()
    bytes_length = checker.get_text_length_from_bytes(file_content)
    end_time = time.time()
    
    results['bytes_parsing'] = {
        'length': bytes_length,
        'time': end_time - start_time
    }
    
    print(f"{'bytes_parsing':15}: {bytes_length:,} chars in {end_time - start_time:.3f}s")
    
    # Find the fastest method
    fastest_method = min(results.keys(), key=lambda x: results[x]['time'])
    print(f"\nFastest method: {fastest_method} ({results[fastest_method]['time']:.3f}s)")
    
    # Verify results are consistent (within reasonable range)
    lengths = [results[method]['length'] for method in results.keys()]
    if max(lengths) > 0:
        variance = (max(lengths) - min(lengths)) / max(lengths)
        print(f"Result variance: {variance:.2%}")
        
        # Assert that methods give similar results (within 10% variance)
        assert variance < 0.1, f"Methods gave very different results: {lengths}"
    
    # All methods should return positive numbers for valid docx files
    for method_name, result in results.items():
        assert result['length'] >= 0, f"{method_name} returned negative length"
    
    return results

def test_docx_text_size_methods():
    """
    Test different methods to check text size efficiently with default test files
    """
    # Test with available docx files
    test_files = [
        "test2.docx",
        "test_big.docx",
        "test_big_unprocess.docx"
    ]
    
    for filename in test_files:
        file_path = os.path.join(current_dir, "data", filename)
        if not os.path.exists(file_path):
            print(f"Skipping {filename} - file not found")
            continue
        
        try:
            check_docx_text_size(filename)
        except Exception as e:
            print(f"Error testing {filename}: {e}")
            continue

def get_text_size_quick(filename: str, test_data_dir: str = None) -> int:
    """
    Quick method to get text size using the most efficient ZIP parsing method
    
    Args:
        filename: Name of the docx file
        test_data_dir: Directory containing the test file (defaults to tests/data)
    
    Returns:
        Number of characters in the document
    """
    if test_data_dir is None:
        test_data_dir = os.path.join(current_dir, "data")
    
    file_path = os.path.join(test_data_dir, filename)
    
    if not os.path.exists(file_path):
        raise FileNotFoundError(f"File not found: {file_path}")
    
    checker = DocxTextSizeChecker()
    
    # Use the ZIP parsing method as it's typically the most efficient
    text_length = checker.get_text_length_zip_parsing(file_path)
    
    print(f"\nQuick text size check for {filename}: {text_length:,} characters")
    
    return text_length

def test_text_size_quick_check():
    """
    Quick test to demonstrate the most efficient method with default file
    """
    try:
        text_length = get_text_size_quick("test2.docx")
        
        # Assert it's a reasonable size
        assert text_length > 0, "Should have some text content"
        assert text_length < 10_000_000, "Should be reasonable size"
    except FileNotFoundError:
        pytest.skip("test2.docx not found")

def get_text_size_from_bytes(file_content: bytes) -> int:
    """
    Get text size directly from file bytes without disk I/O
    
    Args:
        file_content: Raw bytes content of the docx file
    
    Returns:
        Number of characters in the document
    """
    checker = DocxTextSizeChecker()
    text_length = checker.get_text_length_from_bytes(file_content)
    
    print(f"\nText size from bytes: {text_length:,} characters")
    
    return text_length

def check_text_size_from_file(filename: str, test_data_dir: str = None) -> tuple:
    """
    Check text size using both file and bytes methods for comparison
    
    Args:
        filename: Name of the docx file
        test_data_dir: Directory containing the test file (defaults to tests/data)
    
    Returns:
        Tuple of (bytes_length, file_length) for comparison
    """
    if test_data_dir is None:
        test_data_dir = os.path.join(current_dir, "data")
    
    file_path = os.path.join(test_data_dir, filename)
    
    if not os.path.exists(file_path):
        raise FileNotFoundError(f"File not found: {file_path}")
    
    # Read file content
    with open(file_path, 'rb') as f:
        file_content = f.read()
    
    checker = DocxTextSizeChecker()
    
    # Get text length from bytes
    bytes_length = checker.get_text_length_from_bytes(file_content)
    
    # Compare with file-based method
    file_based_length = checker.get_text_length_zip_parsing(file_path)
    
    print(f"\nText size comparison for {filename}:")
    print(f"From bytes: {bytes_length:,} characters")
    print(f"From file:  {file_based_length:,} characters")
    
    return bytes_length, file_based_length

def simple_docx_text_count(file_path: str) -> int:
    """
    Simple function to get text character count from any docx file path
    
    Args:
        file_path: Full path to the docx file
    
    Returns:
        Number of characters in the document
    
    Example:
        count = simple_docx_text_count("/path/to/document.docx")
        print(f"Document has {count:,} characters")
    """
    if not os.path.exists(file_path):
        raise FileNotFoundError(f"File not found: {file_path}")
    
    if not file_path.lower().endswith('.docx'):
        raise ValueError("File must be a .docx file")
    
    checker = DocxTextSizeChecker()
    return checker.get_text_length_zip_parsing(file_path)


if __name__ == "__main__":
    # Example usage - you can change the filename here
    test_filename = "test_big_unprocess.docx"  # Change this to test different files
    
    print("=== DOCX Text Size Checker ===")
    print(f"Testing with file: {test_filename}")
    
    try:
        # Method 1: Comprehensive comparison of all methods
        print("\n1. Comprehensive method comparison:")
        results = check_docx_text_size(test_filename)
        
        # Method 2: Quick size check (most efficient)
        print("\n2. Quick size check:")
        quick_size = get_text_size_quick(test_filename)
        
        # Method 3: From bytes comparison
        print("\n3. Bytes vs file comparison:")
        bytes_len, file_len = check_text_size_from_file(test_filename)
        
        print(f"\n=== Summary for {test_filename} ===")
        print(f"Text length: {quick_size:,} characters")
        print(f"Fastest method: {min(results.keys(), key=lambda x: results[x]['time'])}")
        
        # Method 4: Simple direct count (most convenient)
        print("\n4. Simple direct count:")
        simple_count = simple_docx_text_count(os.path.join(current_dir, "data", test_filename))
        print(f"Simple count: {simple_count:,} characters")
        
        print("\nAll methods completed successfully!")
        
    except FileNotFoundError as e:
        print(f"Error: {e}")
        print("Available files in tests/data/:")
        data_dir = os.path.join(current_dir, "data")
        if os.path.exists(data_dir):
            for file in os.listdir(data_dir):
                if file.endswith('.docx'):
                    print(f"  - {file}")
    except Exception as e:
        print(f"Error processing file: {e}")
    
