import os
import sys
import unittest
import requests
from botocore.exceptions import ClientError

import dotenv
dotenv.load_dotenv()

current_dir = os.path.dirname(__file__)
s3_path = os.path.abspath(os.path.join(current_dir, '..', 'src'))
sys.path.insert(0, s3_path)

from ingest.s3 import S3Helper



class TestS3Helper(unittest.TestCase):

    def test_s3_file_operations(self):

        # Read content from data/test.pdf
        test_file_path = os.path.join(current_dir, "data", "test.pdf")
        with open(test_file_path, "rb") as f:
            original_content = f.read()

        # Initialize S3Helper
        helper = S3Helper()
        doc_id = "123"
        test_file_name = "test.pdf"

        # 1. Upload
        location = helper.file_upload(test_file_name, original_content, doc_id)
        self.assertEqual(location, f"{doc_id}/{test_file_name}")

        # 2. Download and Compare content
        downloaded_content = helper.file_download(location)
        self.assertEqual(downloaded_content, original_content)

        # 3. Generate Presigned File Link
        presigned_url = helper.file_link(location, expiration=600)
        self.assertIsNotNone(presigned_url)
        response = requests.get(presigned_url)
        self.assertEqual(response.content, original_content)

        # 4. Remove File
        helper.file_remove(location)

        # Confirm file removal by checking that a download now fails.
        with self.assertRaises(ClientError):
            helper.file_download(location)


# if __name__ == "__main__":
#     unittest.main()
