from markitdown import MarkItDown
import sys
import os

def convert_docx_to_markdown(input_file_path, output_file_path=None):
    """
    Convert a DOCX file to Markdown using MarkItDown.
    
    Args:
        input_file_path (str): Path to the input DOCX file
        output_file_path (str, optional): Path to save the markdown output. 
                                        If None, prints to console.
    
    Returns:
        str: The markdown content
    """
    if not os.path.exists(input_file_path):
        raise FileNotFoundError(f"Input file not found: {input_file_path}")
    
    # Convert the file to markdown
    markdown_content = MarkItDown().convert(input_file_path).text_content
    
    # Save to output file if specified, otherwise print to console
    if output_file_path:
        with open(output_file_path, 'w', encoding='utf-8') as f:
            f.write(markdown_content)
        print(f"Markdown saved to: {output_file_path}")
    else:
        print(markdown_content)
    
    return markdown_content

def main():
    """Main function to handle command line arguments and file conversion."""
    
    # Default input and output file paths
    input_file = "abbyy/test_big_ocr_30.docx"  # Default input file
    output_file = "abbyy/test_big_ocr_30.md"  # Default output file
    
    # Check for command line arguments
    if len(sys.argv) > 1:
        input_file = sys.argv[1]
    
    if len(sys.argv) > 2:
        output_file = sys.argv[2]
    
    try:
        print(f"Converting {input_file} to markdown...")
        convert_docx_to_markdown(input_file, output_file)
        print("Conversion completed successfully!")
        
    except FileNotFoundError as e:
        print(f"Error: {e}")
        sys.exit(1)
    except Exception as e:
        print(f"An error occurred: {e}")
        sys.exit(1)

if __name__ == "__main__":
    main()