"""
PDF Text Size Checker

This module provides efficient methods to check the text size (character count) 
of PDF files without loading the entire content into memory.

Usage Examples:
    # Quick check with default test file
    python test_pdf_text_size.py
    
    # Check specific file by changing test_filename variable
    test_filename = "your_file.pdf"
    
    # Use in your code:
    from test_pdf_text_size import get_text_size_quick
    size = get_text_size_quick("document.pdf", "/path/to/files")
    
    # Or comprehensive comparison:
    from test_pdf_text_size import check_pdf_text_size
    results = check_pdf_text_size("document.pdf", "/path/to/files")
"""

import os
import sys
import time
from io import BytesIO
import pytest

# Add src directory to path
current_dir = os.path.dirname(__file__)
server_path = os.path.abspath(os.path.join(current_dir, '..', 'src'))
sys.path.insert(0, server_path)

# Import PDF processing libraries with fallbacks
try:
    import PyPDF2
    PYPDF2_AVAILABLE = True
except ImportError:
    PyPDF2 = None
    PYPDF2_AVAILABLE = False

try:
    import pdfplumber
    PDFPLUMBER_AVAILABLE = True
except ImportError:
    pdfplumber = None
    PDFPLUMBER_AVAILABLE = False

try:
    import pymupdf
    PYMUPDF_AVAILABLE = True
except ImportError:
    pymupdf = None
    PYMUPDF_AVAILABLE = False

try:
    from pdfminer.high_level import extract_text
    from pdfminer.layout import LAParams
    PDFMINER_AVAILABLE = True
except ImportError:
    extract_text = None
    LAParams = None
    PDFMINER_AVAILABLE = False

class PdfTextSizeChecker:
    """
    Collection of methods to efficiently check text size in PDF files 
    without loading the entire content into memory where possible
    """
    
    @staticmethod
    def get_text_length_pypdf2(file_path: str) -> int:
        """
        Use PyPDF2 to extract text and return only the length
        Memory efficient as we process page by page
        """
        if not PYPDF2_AVAILABLE:
            print("PyPDF2 not available")
            return 0
            
        try:
            total_length = 0
            with open(file_path, 'rb') as file:
                pdf_reader = PyPDF2.PdfReader(file)
                
                # Process page by page to avoid loading all text at once
                for page_num in range(len(pdf_reader.pages)):
                    page = pdf_reader.pages[page_num]
                    text = page.extract_text()
                    if text:
                        total_length += len(text)
                        
            return total_length
        except Exception as e:
            print(f"Error with PyPDF2: {e}")
            return 0
    
    @staticmethod
    def get_text_length_pdfplumber(file_path: str) -> int:
        """
        Use pdfplumber to extract text and return only the length
        Memory efficient as we process page by page
        """
        if not PDFPLUMBER_AVAILABLE:
            print("pdfplumber not available")
            return 0
            
        try:
            total_length = 0
            with pdfplumber.open(file_path) as pdf:
                # Process page by page to avoid loading all text at once
                for page in pdf.pages:
                    text = page.extract_text()
                    if text:
                        total_length += len(text)
                        
            return total_length
        except Exception as e:
            print(f"Error with pdfplumber: {e}")
            return 0
    
    @staticmethod
    def get_text_length_pymupdf(file_path: str) -> int:
        """
        Use PyMuPDF (pymupdf) to extract text and return only the length
        Memory efficient as we process page by page
        """
        if not PYMUPDF_AVAILABLE:
            print("PyMuPDF not available")
            return 0
            
        try:
            total_length = 0
            doc = pymupdf.open(file_path)
            
            # Process page by page to avoid loading all text at once
            for page_num in range(len(doc)):
                page = doc.load_page(page_num)
                text = page.get_text()
                if text:
                    total_length += len(text)
                    
            doc.close()
            return total_length
        except Exception as e:
            print(f"Error with PyMuPDF: {e}")
            return 0
    
    @staticmethod
    def get_text_length_pdfminer(file_path: str) -> int:
        """
        Use pdfminer to extract text and return only the length
        Note: pdfminer loads the entire document, so less memory efficient
        """
        if not PDFMINER_AVAILABLE:
            print("pdfminer not available")
            return 0
            
        try:
            # Configure layout parameters for better text extraction
            laparams = LAParams(
                line_margin=0.5,
                word_margin=0.1,
                char_margin=2.0,
                boxes_flow=0.5,
                all_texts=False
            )
            
            text = extract_text(file_path, laparams=laparams)
            return len(text) if text else 0
        except Exception as e:
            print(f"Error with pdfminer: {e}")
            return 0
    
    @staticmethod
    def get_text_length_from_bytes_pypdf2(file_content: bytes) -> int:
        """
        Extract text length directly from file bytes using PyPDF2
        Memory efficient implementation processing page by page
        """
        if not PYPDF2_AVAILABLE:
            print("PyPDF2 not available")
            return 0
            
        try:
            total_length = 0
            pdf_reader = PyPDF2.PdfReader(BytesIO(file_content))
            
            # Process page by page to avoid loading all text at once
            for page_num in range(len(pdf_reader.pages)):
                page = pdf_reader.pages[page_num]
                text = page.extract_text()
                if text:
                    total_length += len(text)
                    
            return total_length
        except Exception as e:
            print(f"Error with PyPDF2 bytes: {e}")
            return 0
    
    @staticmethod
    def get_text_length_from_bytes_pdfplumber(file_content: bytes) -> int:
        """
        Extract text length directly from file bytes using pdfplumber
        Memory efficient implementation processing page by page
        """
        if not PDFPLUMBER_AVAILABLE:
            print("pdfplumber not available")
            return 0
            
        try:
            total_length = 0
            with pdfplumber.open(BytesIO(file_content)) as pdf:
                # Process page by page to avoid loading all text at once
                for page in pdf.pages:
                    text = page.extract_text()
                    if text:
                        total_length += len(text)
                        
            return total_length
        except Exception as e:
            print(f"Error with pdfplumber bytes: {e}")
            return 0
    
    @staticmethod
    def get_text_length_from_bytes_pymupdf(file_content: bytes) -> int:
        """
        Extract text length directly from file bytes using PyMuPDF (pymupdf)
        Memory efficient implementation processing page by page
        """
        if not PYMUPDF_AVAILABLE:
            print("PyMuPDF not available")
            return 0
            
        try:
            total_length = 0
            doc = pymupdf.open(stream=file_content, filetype="pdf")
            
            # Process page by page to avoid loading all text at once
            for page_num in range(len(doc)):
                page = doc.load_page(page_num)
                text = page.get_text()
                if text:
                    total_length += len(text)
                    
            doc.close()
            return total_length
        except Exception as e:
            print(f"Error with PyMuPDF bytes: {e}")
            return 0

def check_pdf_text_size(filename: str, test_data_dir: str = None) -> dict:
    """
    Check text size of a PDF file using different methods
    
    Args:
        filename: Name of the PDF file to test
        test_data_dir: Directory containing the test file (defaults to tests/data)
    
    Returns:
        Dictionary with results from different methods
    """
    if test_data_dir is None:
        test_data_dir = os.path.join(current_dir, "data")
    
    file_path = os.path.join(test_data_dir, filename)
    
    if not os.path.exists(file_path):
        raise FileNotFoundError(f"File not found: {file_path}")
    
    checker = PdfTextSizeChecker()
    
    print(f"\n{'='*50}")
    print(f"Testing file: {filename}")
    print(f"File size: {os.path.getsize(file_path) / 1024 / 1024:.2f} MB")
    print(f"{'='*50}")
    
    # Test each available method and measure time
    methods = []
    
    if PYPDF2_AVAILABLE:
        methods.append(("pypdf2", checker.get_text_length_pypdf2))
    
    if PDFPLUMBER_AVAILABLE:
        methods.append(("pdfplumber", checker.get_text_length_pdfplumber))
    
    if PYMUPDF_AVAILABLE:
        methods.append(("pymupdf", checker.get_text_length_pymupdf))
    
    if PDFMINER_AVAILABLE:
        methods.append(("pdfminer", checker.get_text_length_pdfminer))
    
    if not methods:
        print("No PDF processing libraries available!")
        print("Install one of: pip install PyPDF2, pip install pdfplumber, pip install PyMuPDF, pip install pdfminer.six")
        return {}
    
    results = {}
    
    for method_name, method_func in methods:
        start_time = time.time()
        text_length = method_func(file_path)
        end_time = time.time()
        
        results[method_name] = {
            'length': text_length,
            'time': end_time - start_time
        }
        
        print(f"{method_name:15}: {text_length:,} chars in {end_time - start_time:.3f}s")
    
    # Test bytes methods for available libraries
    bytes_methods = []
    
    if PYPDF2_AVAILABLE:
        bytes_methods.append(("pypdf2_bytes", checker.get_text_length_from_bytes_pypdf2))
    
    if PDFPLUMBER_AVAILABLE:
        bytes_methods.append(("pdfplumber_bytes", checker.get_text_length_from_bytes_pdfplumber))
    
    if PYMUPDF_AVAILABLE:
        bytes_methods.append(("pymupdf_bytes", checker.get_text_length_from_bytes_pymupdf))
    
    # Read file once for all bytes methods
    if bytes_methods:
        with open(file_path, 'rb') as f:
            file_content = f.read()
        
        for method_name, method_func in bytes_methods:
            start_time = time.time()
            text_length = method_func(file_content)
            end_time = time.time()
            
            results[method_name] = {
                'length': text_length,
                'time': end_time - start_time
            }
            
            print(f"{method_name:15}: {text_length:,} chars in {end_time - start_time:.3f}s")
    
    # Find the fastest method
    if results:
        fastest_method = min(results.keys(), key=lambda x: results[x]['time'])
        print(f"\nFastest method: {fastest_method} ({results[fastest_method]['time']:.3f}s)")
        
        # Verify results are reasonably consistent
        lengths = [results[method]['length'] for method in results.keys() if results[method]['length'] > 0]
        if lengths and max(lengths) > 0:
            variance = (max(lengths) - min(lengths)) / max(lengths)
            print(f"Result variance: {variance:.2%}")
            
            # Note: PDF libraries can give different results due to different text extraction approaches
            # So we use a more lenient variance threshold
            if variance > 0.5:  # 50% variance threshold
                print(f"Warning: Methods gave significantly different results: {[results[method]['length'] for method in results.keys()]}")
    
    # All methods should return non-negative numbers
    for method_name, result in results.items():
        assert result['length'] >= 0, f"{method_name} returned negative length"
    
    return results

def test_pdf_text_size_methods():
    """
    Test different methods to check text size efficiently with default test files
    """
    # Test with available PDF files
    test_files = [
        "test.pdf",
        "test_ocr.pdf"
    ]
    
    for filename in test_files:
        file_path = os.path.join(current_dir, "data", filename)
        if not os.path.exists(file_path):
            print(f"Skipping {filename} - file not found")
            continue
        
        try:
            check_pdf_text_size(filename)
        except Exception as e:
            print(f"Error testing {filename}: {e}")
            continue

def get_text_size_quick(filename: str, test_data_dir: str = None) -> int:
    """
    Quick method to get text size using the best available method
    
    Args:
        filename: Name of the PDF file
        test_data_dir: Directory containing the test file (defaults to tests/data)
    
    Returns:
        Number of characters in the document
    """
    if test_data_dir is None:
        test_data_dir = os.path.join(current_dir, "data")
    
    file_path = os.path.join(test_data_dir, filename)
    
    if not os.path.exists(file_path):
        raise FileNotFoundError(f"File not found: {file_path}")
    
    checker = PdfTextSizeChecker()
    
    # Use the best available method (prefer PyMuPDF for speed, fallback to others)
    if PYMUPDF_AVAILABLE:
        text_length = checker.get_text_length_pymupdf(file_path)
    elif PDFPLUMBER_AVAILABLE:
        text_length = checker.get_text_length_pdfplumber(file_path)
    elif PYPDF2_AVAILABLE:
        text_length = checker.get_text_length_pypdf2(file_path)
    elif PDFMINER_AVAILABLE:
        text_length = checker.get_text_length_pdfminer(file_path)
    else:
        raise ImportError("No PDF processing libraries available! Install one of: PyPDF2, pdfplumber, PyMuPDF, pdfminer.six")
    
    print(f"\nQuick text size check for {filename}: {text_length:,} characters")
    
    return text_length

def test_text_size_quick_check():
    """
    Quick test to demonstrate the most efficient method with default file
    """
    try:
        text_length = get_text_size_quick("test.pdf")
        
        # Assert it's a reasonable size
        assert text_length >= 0, "Should have non-negative text content"
        assert text_length < 10_000_000, "Should be reasonable size"
    except FileNotFoundError:
        pytest.skip("test.pdf not found")
    except ImportError:
        pytest.skip("No PDF processing libraries available")

def get_text_size_from_bytes(file_content: bytes) -> int:
    """
    Get text size directly from file bytes without disk I/O
    
    Args:
        file_content: Raw bytes content of the PDF file
    
    Returns:
        Number of characters in the document
    """
    checker = PdfTextSizeChecker()
    
    # Use the best available method for bytes processing
    if PYMUPDF_AVAILABLE:
        text_length = checker.get_text_length_from_bytes_pymupdf(file_content)
    elif PDFPLUMBER_AVAILABLE:
        text_length = checker.get_text_length_from_bytes_pdfplumber(file_content)
    elif PYPDF2_AVAILABLE:
        text_length = checker.get_text_length_from_bytes_pypdf2(file_content)
    else:
        raise ImportError("No PDF processing libraries available for bytes processing!")
    
    print(f"\nText size from bytes: {text_length:,} characters")
    
    return text_length

def check_text_size_from_file(filename: str, test_data_dir: str = None) -> tuple:
    """
    Check text size using both file and bytes methods for comparison
    
    Args:
        filename: Name of the PDF file
        test_data_dir: Directory containing the test file (defaults to tests/data)
    
    Returns:
        Tuple of (bytes_length, file_length) for comparison
    """
    if test_data_dir is None:
        test_data_dir = os.path.join(current_dir, "data")
    
    file_path = os.path.join(test_data_dir, filename)
    
    if not os.path.exists(file_path):
        raise FileNotFoundError(f"File not found: {file_path}")
    
    # Read file content
    with open(file_path, 'rb') as f:
        file_content = f.read()
    
    checker = PdfTextSizeChecker()
    
    # Get text length from bytes using best available method
    if PYMUPDF_AVAILABLE:
        bytes_length = checker.get_text_length_from_bytes_pymupdf(file_content)
        file_based_length = checker.get_text_length_pymupdf(file_path)
    elif PDFPLUMBER_AVAILABLE:
        bytes_length = checker.get_text_length_from_bytes_pdfplumber(file_content)
        file_based_length = checker.get_text_length_pdfplumber(file_path)
    elif PYPDF2_AVAILABLE:
        bytes_length = checker.get_text_length_from_bytes_pypdf2(file_content)
        file_based_length = checker.get_text_length_pypdf2(file_path)
    else:
        raise ImportError("No PDF processing libraries available!")
    
    print(f"\nText size comparison for {filename}:")
    print(f"From bytes: {bytes_length:,} characters")
    print(f"From file:  {file_based_length:,} characters")
    
    return bytes_length, file_based_length

def simple_pdf_text_count(file_path: str) -> int:
    """
    Simple function to get text character count from any PDF file path
    
    Args:
        file_path: Full path to the PDF file
    
    Returns:
        Number of characters in the document
    
    Example:
        count = simple_pdf_text_count("/path/to/document.pdf")
        print(f"Document has {count:,} characters")
    """
    if not os.path.exists(file_path):
        raise FileNotFoundError(f"File not found: {file_path}")
    
    if not file_path.lower().endswith('.pdf'):
        raise ValueError("File must be a .pdf file")
    
    checker = PdfTextSizeChecker()
    
    # Use the best available method
    if PYMUPDF_AVAILABLE:
        return checker.get_text_length_pymupdf(file_path)
    elif PDFPLUMBER_AVAILABLE:
        return checker.get_text_length_pdfplumber(file_path)
    elif PYPDF2_AVAILABLE:
        return checker.get_text_length_pypdf2(file_path)
    elif PDFMINER_AVAILABLE:
        return checker.get_text_length_pdfminer(file_path)
    else:
        raise ImportError("No PDF processing libraries available! Install one of: PyPDF2, pdfplumber, PyMuPDF, pdfminer.six")

def get_available_libraries():
    """
    Get information about available PDF processing libraries
    
    Returns:
        Dictionary with library availability and recommendations
    """
    libraries = {
        'PyPDF2': {
            'available': PYPDF2_AVAILABLE,
            'install': 'pip install PyPDF2',
            'description': 'Basic PDF text extraction, good for simple documents'
        },
        'pdfplumber': {
            'available': PDFPLUMBER_AVAILABLE,
            'install': 'pip install pdfplumber',
            'description': 'Good for structured text extraction, handles tables well'
        },
        'PyMuPDF': {
            'available': PYMUPDF_AVAILABLE,
            'install': 'pip install PyMuPDF',
            'description': 'Fast and accurate text extraction, recommended for performance'
        },
        'pdfminer': {
            'available': PDFMINER_AVAILABLE,
            'install': 'pip install pdfminer.six',
            'description': 'Thorough text extraction, good for complex layouts'
        }
    }
    
    return libraries

if __name__ == "__main__":
    # Example usage - you can change the filename here
    test_filename = "test.pdf"  # Change this to test different files
    
    print("=== PDF Text Size Checker ===")
    print(f"Testing with file: {test_filename}")
    
    # Check available libraries
    libraries = get_available_libraries()
    available_libs = [name for name, info in libraries.items() if info['available']]
    
    if not available_libs:
        print("\nNo PDF processing libraries available!")
        print("Please install one of the following:")
        for name, info in libraries.items():
            print(f"  {name}: {info['install']}")
            print(f"    {info['description']}")
        sys.exit(1)
    
    print(f"\nAvailable libraries: {', '.join(available_libs)}")
    
    try:
        # Method 1: Comprehensive comparison of all methods
        print("\n1. Comprehensive method comparison:")
        results = check_pdf_text_size(test_filename)
        
        if results:
            # Method 2: Quick size check (most efficient)
            print("\n2. Quick size check:")
            quick_size = get_text_size_quick(test_filename)
            
            # Method 3: From bytes comparison
            print("\n3. Bytes vs file comparison:")
            bytes_len, file_len = check_text_size_from_file(test_filename)
            
            print(f"\n=== Summary for {test_filename} ===")
            print(f"Text length: {quick_size:,} characters")
            print(f"Fastest method: {min(results.keys(), key=lambda x: results[x]['time'])}")
            
            # Method 4: Simple direct count (most convenient)
            print("\n4. Simple direct count:")
            simple_count = simple_pdf_text_count(os.path.join(current_dir, "data", test_filename))
            print(f"Simple count: {simple_count:,} characters")
            
            print("\nAll methods completed successfully!")
        else:
            print("No results obtained - check if libraries are properly installed")
        
    except FileNotFoundError as e:
        print(f"Error: {e}")
        print("Available files in tests/data/:")
        data_dir = os.path.join(current_dir, "data")
        if os.path.exists(data_dir):
            for file in os.listdir(data_dir):
                if file.endswith('.pdf'):
                    print(f"  - {file}")
    except Exception as e:
        print(f"Error processing file: {e}") 