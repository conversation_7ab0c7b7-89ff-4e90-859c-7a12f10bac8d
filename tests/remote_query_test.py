import os
import time
import uuid
import json

import dotenv
import requests

dotenv.load_dotenv()

HOST = "http://localhost:4116"


def timed_request(method, url, description, **kwargs):
    """Helper function to time HTTP requests and print timing information"""
    start_time = time.time()
    response = getattr(requests, method)(url, **kwargs)
    duration = time.time() - start_time
    print(f"⏱️  {description}: {duration:.3f} seconds")
    return response


def test_chat_endpoint():
    """Test the /api/km/chat endpoint functionality"""
    print("🚀 Starting chat endpoint test...")
    
    # Generate test IDs
    community_id = str(uuid.uuid4())
    user_id = str(uuid.uuid4())
    company_id = str(uuid.uuid4())
    
    print(f"📋 Test Configuration:")
    print(f"   User ID: {user_id}")
    print(f"   Community ID: {community_id}")
    print(f"   Company ID: {company_id}")
    print(f"   Host: {HOST}")
    print()
    
    # Define chat payload and parameters
    chat_payload = {
        "question": "What is the content of the document?",
        "history": []
    }
    
    chat_params = {
        "model": "kimi2",
        "lang": "en",
        "user": user_id,
        "tax": community_id,
        "company_id": company_id
    }
    
    print("📤 Chat Request Details:")
    print(f"   Endpoint: {HOST}/api/km/chat")
    print(f"   Payload: {json.dumps(chat_payload, indent=2)}")
    print(f"   Parameters: {json.dumps(chat_params, indent=2)}")
    print()
    
    # Make the chat request
    try:
        response = timed_request(
            "post",
            f"{HOST}/api/km/chat",
            "Chat endpoint",
            params=chat_params,
            json=chat_payload
        )
        
        print(f"📥 Response Details:")
        print(f"   Status Code: {response.status_code}")
        print(f"   Headers: {dict(response.headers)}")
        print()
        
        if response.status_code == 200:
            print("✅ Chat endpoint test PASSED")
            print("📄 Response Content:")
            try:
                response_json = response.json()
                print(json.dumps(response_json, indent=2, ensure_ascii=False))
            except json.JSONDecodeError:
                print("Response is not valid JSON:")
                print(response.text)
        else:
            print(f"❌ Chat endpoint test FAILED")
            print(f"   Error: {response.status_code} - {response.text}")
            
    except requests.exceptions.RequestException as e:
        print(f"❌ Request failed with exception: {e}")
    except Exception as e:
        print(f"❌ Unexpected error: {e}")
    
    print("\n" + "="*50)
    print("Chat endpoint test completed")


if __name__ == "__main__":
    test_chat_endpoint()
