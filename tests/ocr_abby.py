import sys
import time
import xml.etree.ElementTree as ET
import requests

def ocr_to_text(input_path, output_path,
                host, app_id, password):
    session = requests.Session()

    # 1. submit image for plain-text output
    # Read file as binary data (matching reference implementation)
    with open(input_path, 'rb') as image_file:
        image_data = image_file.read()
    
    resp = session.post(
        f"{host}/processImage",
        params={"exportFormat": "docx"},
        data=image_data,  # Use data instead of files
        auth=(app_id, password)  # Use tuple auth instead of HTTPBasicAuth
    )
    resp.raise_for_status()
    task = ET.fromstring(resp.text).find("task")
    task_id = task.get("id")

    # 2. poll until done or failed
    status = task.get("status")
    while status not in ("Completed", "Failed"):
        time.sleep(2)
        resp = session.get(
            f"{host}/getTaskStatus",
            params={"taskId": task_id},
            auth=(app_id, password)
        )
        resp.raise_for_status()
        task = ET.fromstring(resp.text).find("task")
        status = task.get("status")

    if status != "Completed":
        raise RuntimeError(f"OCR failed: {task.get('error')}")

    # 3. download the single TXT result
    url = task.get("resultUrl")
    if not url:
        raise RuntimeError("No result URL returned")
    txt = session.get(url).content
    with open(output_path, "wb") as f:
        f.write(txt)

    # 4. optional: delete task to free server resources
    session.get(f"{host}/deleteTask", params={"taskId": task_id}, auth=(app_id, password))


if __name__ == "__main__":
    # configure these however you like (env vars, hard-code, etc.)
    # Note: Using EU server as in reference implementation
    # Change to http://cloud-westus.ocrsdk.com if your app is in US region
    ABBYY_HOST = "http://cloud-eu.ocrsdk.com"
    APP_ID      = "217f6250-2ddf-4e61-ac6d-5861f77ee0c1"
    PASSWORD    = "+dZOQUGffk0zppOrlIBcSFGM"

    input_file = "data/test_big_ocr_30.pdf"
    output_file = "abbyy/test_big_ocr_30.docx"
    ocr_to_text(    
        input_file, output_file,
        host=ABBYY_HOST,
        app_id=APP_ID,
        password=PASSWORD
    )
    print(f"OCR complete → {output_file}")
