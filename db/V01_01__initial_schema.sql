
CREATE TABLE IF NOT EXISTS km.chat (
	id uuid NOT NULL,
	user_id uuid NOT NULL,
	app text NULL,
	llm text NULL,
	tax text NULL,
	lang text NULL,
	question text NULL,
	answer text NULL,
	score int4 NULL,
	"comment" text NULL,
	sources jsonb NULL,
	history jsonb NULL,
	"data" jsonb NULL,
	created timestamptz NOT NULL DEFAULT now(),
	updated timestamptz NOT NULL DEFAULT now(),
	CONSTRAINT chat_pkey PRIMARY KEY (id)
);