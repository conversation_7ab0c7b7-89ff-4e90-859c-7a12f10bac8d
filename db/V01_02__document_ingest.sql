CREATE TYPE km.document_status AS ENUM ('ERROR', 'UPLOADED', 'INGESTED', 'REMOVED');

CREATE TABLE IF NOT EXISTS  km.document_ingest (
    id uuid PRIMARY KEY,
    community_id uuid NOT NULL,
    status km.document_status NOT NULL,
    error_message text,
    file_name text NOT NULL,
    file_location text NOT NULL,
    file_upload_date timestamp with time zone NOT NULL DEFAULT now(),
    updated timestamp with time zone NOT NULL DEFAULT now(),
    translation_document_id uuid,
    upload_user_id uuid
);

CREATE INDEX IF NOT EXISTS idx_document_ingest_community_id ON km.document_ingest(community_id);
