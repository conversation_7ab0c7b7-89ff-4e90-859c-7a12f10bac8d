-- Add OCR status to the document_status enum
-- Step 1: Change column type from enum to varchar
ALTER TABLE km.document_ingest
    ALTER COLUMN status TYPE VARCHAR(255);

-- Step 2: Drop and create the enum again with new values
DROP TYPE IF EXISTS km.document_status;
CREATE TYPE km.document_status AS ENUM (
    'ERROR',
    'UPLOADING',
    'UPLOADED',
    'OCR',
    'INGESTED',
    'REMOVED'
);

-- Step 3: Revert column type back to the enum
ALTER TABLE km.document_ingest
    ALTER COLUMN status TYPE km.document_status
    USING (status::km.document_status); 