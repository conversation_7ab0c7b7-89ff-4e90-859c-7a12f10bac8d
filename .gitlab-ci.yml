image: registry.gitlab.com/exfluency/pipeline-scripts

services:
  - name: docker:dind
    entrypoint: ["env", "-u", "DOCKER_HOST"]
    command: ["dockerd-entrypoint.sh"]

variables:
  DOCKER_DRIVER: overlay2
  DOCKER_HOST: tcp://docker:2375/
  DOCKER_TLS_CERTDIR: ""

stages:
  - image_build
  - deploy

include:
  - local: ci/image-build.yml
  - local: ci/test-update-deploy.yml
  - local: ci/demo-update-deploy.yml
  - local: ci/main-update-deploy.yml


